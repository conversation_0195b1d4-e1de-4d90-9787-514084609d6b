package main

import (
	"flag"
	"os"
	"path/filepath"

	"github.com/alading/scm/front/kit/scripts/contract2kit/formatters/contract"
)

var path string
var out string
var formatter string
var scope string

func format(f string) {
	switch formatter {
	case "contract":
		contract.Format(path, scope, f)
	}
}

func walkFile(path string, fn func(f string) error) {
	err := filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		return fn(path)
	})
	if err != nil {
		panic(err)
	}
}

func init() {
	flag.StringVar(&path, "path", "", "path to the directory containing the migrated vue files")
	flag.StringVar(&formatter, "formatter", "", "formatter to use for the import paths")
	flag.StringVar(&scope, "scope", "", "scope of the formatter")
	flag.StringVar(&out, "out", "", "path to the directory to write the formatted files to")
}
func main() {
	flag.Parse()

	if path == "" {
		panic("path is required")
	}

	if formatter == "" {
		panic("formatter is required")
	}

	if scope == "" {
		panic("scope is required")
	}

	if out == "" {
		out = path
	}

	walkFile(path, func(f string) error {
		// println("formatting file: " + f)
		format(f)
		return nil
	})
}
