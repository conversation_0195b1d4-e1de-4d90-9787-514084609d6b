import { spawn } from 'child_process'
import { walk } from './utils'
import path from 'path'
import fs from 'fs'

const fmtFiles = done => {
  const prettier = spawn('./node_modules/.bin/prettier', [
    '--config',
    '.prettierrc',
    '--write',
    './src/components',
    './src/pages',
    './src/services',
    './src/models',
    './src/formatters',
    './src/helpers'
  ])

  prettier.stdout.on('data', function (data) {
    console.log(data.toString())
  })

  prettier.stderr.on('data', function (data) {
    console.log(data.toString())
  })

  prettier.on('exit', function (code) {
    done()
  })
}

const fmtImport = f => {
  const ext = path.extname(f)
  if (!(ext === '.js' || ext === '.vue')) {
    return
  }

  const reg = /import ([A-z0-9]+) from ["'](.*?)["']/g
  const fr = fs.readFileSync(f)
  var content = fr.toString()
  const rs = content.matchAll(reg)
  var isNeedWrite = false
  for (var matched of rs) {
    const importedName = matched[1]
    const relativePath = matched[2]
    if (relativePath.includes('kit/')) {
      continue
    }
    //略过当前目录下的文件
    if (relativePath[0] === '.' && relativePath[1] === '/') {
      continue
    }

    isNeedWrite = true

    const fDir = path.dirname(f)

    const fmtPath = path.normalize(fDir + '/' + relativePath)

    const tmp = fmtPath.split('src/')

    const fmtImportPath = `kit/${tmp[1]}`

    content = content.replace(
      matched[0],
      `import ${importedName} from '${fmtImportPath}'`
    )
  }
  console.log('fmt import for', f)
  fs.writeFileSync(f, content)
}

fmtFiles(done => {
  walk('src', fmtImport)
})

// walk('src', fmtImport)
