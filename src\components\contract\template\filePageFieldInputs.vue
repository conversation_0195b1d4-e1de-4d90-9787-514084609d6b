<template>
  <div class="pageFieldInputs">
    <div :key="field.id" v-for="field in fields" :class="calcClass(field)">
      <div class="pageFieldInput" :id="`field${field.id}`">
        <div class="title">
          {{ field.name }}
          <span style="color: red" v-if="field.writeRequired">*</span>
        </div>
        <el-input
          :ref="field.id"
          :placeholder="`请输入${field.name}`"
          :maxlength="getMaxWords(field)"
          v-model="field.value"
          v-if="field.writeType === WriteTypeSingleLineText"
          @focus="$emit('fieldFocus', field)"
          :style="getInputStyles(field)"
          @change="$emit('change')"
        />
        <el-input
          :ref="field.id"
          :placeholder="`请输入${field.name}`"
          class="textarea"
          type="textarea"
          clearable
          :value="field.value"
          @input="e => changeFieldValue(e, field)"
          :rows="6"
          :maxlength="getMaxWords(field)"
          show-word-limit
          @focus="$emit('fieldFocus', field)"
          v-if="field.writeType === WriteTypeMultiLineText"
          :style="getTextareaStyles(field)"
          @change="$emit('change')"
        />
        <el-date-picker
          :ref="field.id"
          v-model="field.value"
          clearable
          v-if="field.writeType === WriteTypeDate"
          type="date"
          @focus="$emit('fieldFocus', field)"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          :editable="!isMobile"
          @change="$emit('change')"
        />
        <div class="message">请填写{{ field.name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import store from '../../../helpers/store'
import {
  WriteTypeSingleLineText,
  WriteTypeMultiLineText,
  WriteTypeDate,
  FieldTypeCustom
} from '../../../services/contract/constants'
import getPageFieldMaxLinesAndWordAmount from './getPageFieldMaxLinesAndWords'

const getMaxLengthPageField = (pageFields, fieldId) => {
  var min = 1000000
  var pageField = null
  for (var c of pageFields) {
    if (fieldId !== c.fieldId) {
      continue
    }
    const [_, cwords] = getPageFieldMaxLinesAndWordAmount(c)
    if (min > cwords) {
      min = cwords
      pageField = c
    }
  }

  return pageField
}
export default {
  watch: {
    currentPageField: {
      handler(v) {
        if (!v) {
          return
        }
        const refs = this.$refs[v.fieldId]
        if (refs && refs.length) {
          setTimeout(() => refs[0].focus(), 100)
        }
      }
    }
  },
  created() {
    this.closed = store.get('__pageFieldInputsClosed')
  },
  computed: {
    isMobile() {
      if (/Mobi|Android|iPhone/i.test(window.navigator.userAgent)) {
        return true
      }

      return
    }
  },
  methods: {
    calcClass(field) {
      if (!field.value && this.showErrors) {
        return 'error'
      }
      if (this.currentPageField && this.currentPageField.fieldId === field.id) {
        return 'focus'
      }
      return ''
    },
    changeFieldValue(value, field) {
      // const lines = value.trim().split('\n')
      // const maxLines = this.getMaxLines(field)
      // if (lines.length >= maxLines) {
      //   field.value = lines.slice(0, maxLines).join('\n')
      //   return
      // }

      field.value = value
    },
    getInputStyles(field) {
      // const pageField = getMaxLengthPageField(this.pageFields, field.id)
      return {
        // width: `${pageField.width + 22}px`,
        // fontSize: `${pageField.fontSize}px`,
        padding: 0
      }
    },
    getTextareaStyles(field) {
      const pageField = getMaxLengthPageField(this.pageFields, field.id)
      return {
        // width: `${pageField.width}px`,
        fontSize: `${pageField.fontSize}px`,
        padding: 0
      }
    },
    getMaxLines(field) {
      const pageField = getMaxLengthPageField(this.pageFields, field.id)
      const [lines, _] = getPageFieldMaxLinesAndWordAmount(pageField)
      return lines
    },
    getMaxWords(field) {
      const pageField = getMaxLengthPageField(this.pageFields, field.id)
      const [_, words] = getPageFieldMaxLinesAndWordAmount(pageField)
      return words
    },
    getMaxLength(field) {
      const pageField = getMaxLengthPageField(this.pageFields, field.id)
      const [_, words] = getPageFieldMaxLinesAndWordAmount(pageField)
      return words
    }
  },
  props: {
    currentPageField: Object,
    fields: Array,
    files: Array,
    pageFields: Array,
    showErrors: Boolean
  },
  data() {
    return {
      WriteTypeSingleLineText,
      WriteTypeMultiLineText,
      WriteTypeDate,
      FieldTypeCustom
    }
  }
}
</script>
<style scoped>
.pageFieldInputs {
  font-size: 14px;
}
.pageFieldInput {
  margin-bottom: 20px;
}
.pageFieldInput .msg {
  display: none;
}
::v-deep .focus input {
  border-color: var(--o-primary-color);
}
::v-deep .error input {
  border-color: red;
}
::v-deep .focus textarea {
  border-color: var(--o-primary-color);
}
::v-deep .error textarea {
  border-color: red;
}
.message {
  display: none;
}
.error .message {
  display: block;
  color: red;
}
</style>