<template>
  <div
    class="topBar"
    :style="{
      width: 'calc(100vw - 290px)',
      position: 'sticky',
      borderRadius: '8px',
      padding: '0 24px',
      height: '48px',
      lineHeight: '48px',
      zIndex: 999
    }"
  >
    <slot></slot>
    <hr
      :style="{
        border: 'none',
        borderBottom: noBorder ? 'none' : '1px solid #EEF0F4',
        height: '1px',
        padding: 0,
        margin: 0
      }"
    />
  </div>
</template>
<script>
export default {
  props: {
    noBorder: Boolean
  }
}
</script>
<style scoped>
.topBar h1 {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 16px;
  color: #24262a;
  text-align: right;
  margin: 0;
  padding: 0;
}
</style>