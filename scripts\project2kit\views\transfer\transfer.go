package transfer

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/alading/scm/front/kit/scripts/project2kit/helpers"
)

func TransferAPIs(fpath, service string) {
	var newFilePath string
	var newLines []string

	lines := helpers.ReadFileToLines(fpath)
	ext := filepath.Ext(fpath)
	switch ext {
	case ".vue":
		newFilePath, newLines = transAPIs(fpath, lines, service)
	}

	if newFilePath != "" && newFilePath != fpath {
		err := os.Remove(fpath)
		if err != nil {
			panic(err)
		}
	}

	if newFilePath == "" {
		newFilePath = fpath
	}

	// println(newLines)
	err := ioutil.WriteFile(newFilePath, []byte(strings.Join(newLines, "\n")), 0644)
	if err != nil {
		panic(err)
	}

	return
}
