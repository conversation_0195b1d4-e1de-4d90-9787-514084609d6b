import { PageFieldDefaultBorderColor } from '../../../pages/contract/constants'
//定义6个颜色
const colors = [
  '#4F71FF',
  '#FFAC04',
  '#07BB06',
  '#71248D',
  '#c65306',
  '#006e5f',
  '#faff72'
]

var index = 0
var m = {}
//根据字符串，拿出一个指定的颜色
export const getColorByString = s => {
  if (!s) {
    return PageFieldDefaultBorderColor
  }
  if (typeof m[s] === 'undefined') {
    m[s] = index
    index++
  }

  return colors[m[s] % colors.length]
}
