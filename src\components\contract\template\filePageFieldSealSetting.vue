<template>
  <div class="pageFieldSealSetting">
    <div style="display: flex">
      <Title
        :title="legalName ? '企业印章' : '个人签名'"
        style="flex: 1 1 auto"
      />
      <el-button
        v-if="signatureSeal"
        size="small"
        @click="showSelectSealDialog = true"
      >
        更换
      </el-button>
    </div>
    <br />
    <div v-if="!legalName && !signatureSeal">
      合同发起人限定使用手写签名签署，您尚未添加手写签名，请在手机端进行操作。
    </div>
    <template v-if="signatureSeal">
      <div style="text-align: center">
        <img
          :src="
            signatureSeal.sealList.find(item => item.id === currentSealId).url
          "
          style="
            object-fit: scale-down;
            width: 240px;
            height: 240px;
            border: 1px solid #eef0f4;
          "
        />
        <br />
        {{ legalName ? legalName : signerName }}
      </div>
      <br />
      <Title title="日期" />
      <br />
      <el-button
        style="width: 100%; cursor: not-allowed"
        @click="$emit('selectDate', new Date())"
      >
        {{ new Date() | formatDateTime('yyyy年MM月dd日') }}
      </el-button>
    </template>

    <el-dialog
      :visible.sync="showSelectSealDialog"
      :title="legalName ? '更换印章' : '更换个人签名'"
      :close-on-click-modal="false"
    >
      <div style="display: flex; gap: 10px; flex-wrap: wrap">
        <div
          :key="seal.id"
          v-for="seal in seals"
          :style="{
            border: isCurrentSeal(seal)
              ? '1px solid blue'
              : '1px solid #EEF0F4',
            position: 'relative',
            cursor: 'pointer',
            gap: '10px'
          }"
          @click="selectedSeal = seal"
        >
          <img
            style="object-fit: scale-down; width: 110px; height: 110px"
            :src="seal.url"
          />
          <span
            v-if="isDefaultSeal(seal)"
            style="
              background: #bdbdbd;
              border-radius: 5px;
              font-size: 12px;
              padding: 2px;
              color: #fff;
              position: absolute;
              top: 5px;
              left: 5px;
            "
          >
            默认
          </span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showSelectSealDialog = false"> 取消 </el-button>
        <el-button
          type="primary"
          @click="
            () => {
              $emit('select', selectedSeal)
              showSelectSealDialog = false
            }
          "
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Title from '../title.vue'
export default {
  props: {
    signatureSeal: Object,
    currentSealId: String
  },
  data() {
    return {
      showSelectSealDialog: false,
      selectedSeal: {
        id: this.currentSealId
      }
    }
  },
  methods: {
    isCurrentSeal(seal) {
      return seal.id === this.selectedSeal.id
    },
    isDefaultSeal(seal) {
      return seal.id === this.signatureSeal?.defaultSealId
    }
  },
  computed: {
    seals() {
      return this.signatureSeal?.sealList
    },
    legalName() {
      if (this.signatureSeal?.owner.legal) {
        return this.signatureSeal.owner.legal.name
      }
      return ''
    },
    signerName() {
      const seal = this.signatureSeal?.sealList.find(
        item => item.id === this.currentSealId
      )
      if (!seal) {
      }
      if (seal?.type === 'SYSTEM') {
        return '标准签名'
      }
      if (seal?.type === 'HAND_WRITER') {
        return '手绘签名'
      }
      return '默认'
    }
  },
  components: {
    Title
  }
}
</script>