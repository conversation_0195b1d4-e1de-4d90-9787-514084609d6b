<template>
  <div class="files">
    <div
      v-if="!closed && files.length > 1"
      :style="{
        borderRadius: '8px',
        background: '#F8F8F8',
        padding: '10px',
        display: 'flex',
        margin: '10px 0'
      }"
    >
      <span
        :style="{
          color: '#777C94',
          fontSize: '12px'
        }"
      >
        多个文件时，可点击切换文件，针对每个文件设置填充域和签章区
      </span>
      <i
        class="el-icon-close"
        :style="{
          cursor: 'pointer',
          marginLeft: '10x'
        }"
        @click="closeTip"
      />
    </div>
    <div
      :style="{ textAlign: 'center', marginBottom: '22px' }"
      :key="`file${index}`"
      v-for="(file, index) in files"
    >
      <a
        :style="{
          display: 'block',
          cursor: 'pointer'
        }"
        @click="select(index)"
      >
        <div
          :title="file.name"
          class="text-ellipsis-2line"
          style="font-size: 14px; font-weight: 400"
        >
          {{ file.name }}
        </div>

        <img
          width="138"
          :src="file.archiveImageList[0]"
          :style="{
            border:
              currentFileIndex === index
                ? '1px solid #4f71ff'
                : '1px solid #eee'
          }"
        />
        <br />
        <span
          :style="{
            position: 'relative',
            background: '#00000050',
            padding: '1px 4px',
            fontSize: '12px',
            borderRadius: '2px',
            right: '-52px',
            top: '-30px',
            color: '#fff'
          }"
        >
          {{ file.archiveImageList.length }} 页
        </span>
      </a>
    </div>
  </div>
</template>
<script>
import store from '../../../helpers/store'
export default {
  props: {
    currentFileIndex: Number,
    files: {
      type: Array,
      validator(v) {
        for (var file of v) {
          if (!file.name) {
            return false
          }
        }

        return true
      }
    }
  },
  created() {
    this.closed = store.get('__waitingSignatueFilesTip')
  },
  data() {
    return {
      closed: false
    }
  },
  methods: {
    select(index) {
      this.$emit('select', index)
    },
    closeTip() {
      store.set('__waitingSignatueFilesTip', true)
      this.closed = true
    }
  }
}
</script>
<style scoped>
</style>