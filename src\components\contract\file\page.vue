<template>
  <div
    class="page"
    :style="{
      width: `${imgSize[0]}px`,
      height: `${imgSize[1]}px`,
      transform: `translate(0, ${top}px)`,
      position: 'absolute',
      top: 0
    }"
    @dragover="dragover"
    @drop="drop"
  >
    <img @load="loading.close()" :src="src" />
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    imgSize: Array,
    src: String,
    pageNo: Number
  },
  data() {
    return {
      loading: null
    }
  },
  computed: {
    top() {
      return this.imgSize[1] * (this.pageNo - 1) + this.pageNo * 16
    }
  },
  beforeDestroy() {
    this.loading.close()
  },
  mounted() {
    this.loading = this.$loading({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255,255, 0.7)'
    })
  },
  methods: {
    dragover(e) {
      e.preventDefault()
    },
    drop(e) {
      e.preventDefault()
      this.$emit('itemDrop', e)
    }
  }
}
</script>