package transfer

import (
	"strings"
	"testing"
)

func Test_linesIterator(t *testing.T) {
	result := linesIterator([]string{"a", "b", "c"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "b" {
			return []string{"a", "b", "c"}, currentIndex, currentIndex
		}

		return nil, -1, -1
	})

	if strings.Join(result, ",") != "a,a,b,c,c" {
		t.<PERSON>rror("linesIterator failed")
	}

	result = linesIterator([]string{"a", "b", "c", "d"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "c" {
			return []string{"t", "t", "t"}, 0, 3
		}

		return nil, -1, -1
	})

	if strings.Join(result, ",") != "a,b,t,t,t" {
		t.<PERSON>rror("linesIterator failed")
	}

	result = linesIterator([]string{"a", "b", "c", "d"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "b" {
			return []string{"t", "t", "t"}, 0, 2
		}

		return nil, -1, -1
	})

	if strings.Join(result, ",") != "a,t,t,t,d" {
		t.Error("linesIterator failed")
	}
}
