<template>
  <div>
    <Title
      style="margin-top: 24px"
      :title="
        value.owner.signerType === SingerTypeCompany ? '企业印章' : '个人签章'
      "
    />
    <div style="font-size: 12px; color: #24262a; margin: 24px 0 10px 0">
      {{
        (value.owner.legal && value.owner.legal.name) ||
        (value.owner.signer && value.owner.signer.name)
      }}
    </div>
    <el-radio-group @input="handleSealChange" v-model="selectSeal">
      <el-radio
        v-for="seal in value.sealList"
        :label="seal.id"
        :key="seal.id"
        border
        style="height: 135px; width: 140px"
      >
        <div class="select-icon">
          <el-image
            style="height: 135px; width: 140px"
            :src="seal.url"
            :key="seal.id"
          ></el-image>
          <i
            class="olading-iconfont oi-tag_select icon"
            v-if="selectSeal === seal.id"
          ></i>
        </div>
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import Title from '../../../components/title.vue'
import {
  SingerTypePerson,
  SingerTypeCompany
} from '../../../services/contract/constants'
export default {
  data() {
    return {
      selectSeal: '',
      SingerTypeCompany
    }
  },
  created() {
    // 默认选中
    if (this.value.defaultSealId) {
      this.selectSeal = this.value.defaultSealId
      this.handleSealChange(this.value.defaultSealId)
    }
  },
  components: {
    Title
  },
  props: {
    value: {
      type: Object
    }
  },
  methods: {
    handleSealChange(sealId) {
      const signerType = this.value.owner.signerType
      const sealModal = { signerType, sealId }
      // 1为个人 2是企业
      if (signerType === SingerTypePerson) {
        sealModal.userId = this.value.owner.signer.id
      } else {
        sealModal.legalId = this.value.owner.legal.id
      }
      this.$emit('handleSealChange', sealModal)
    }
  }
}
</script>

<style scoped>
.select-icon {
  position: relative;
  font-size: 12px;
}
.select-icon .icon {
  position: absolute;
  font-size: 20px;
  bottom: 2px;
  right: 1px;
}
::v-deep .el-radio__input {
  display: none;
}
::v-deep .el-radio__inner {
  display: none;
}

::v-deep .el-radio__label {
  width: 140px !important;
  height: 135px !important;
  display: inline-block !important;
  padding-left: 0;
}
::v-deep .el-radio.is-bordered {
  padding: 0px !important;
}
</style>