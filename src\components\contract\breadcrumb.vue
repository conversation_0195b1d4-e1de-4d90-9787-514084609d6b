<template>
  <div
    :style="{
      display: 'flex',
      position: 'relative'
    }"
  >
    <a
      @click="$router.back()"
      :style="{
        cursor: 'pointer',
        color: '#4F71FF',
        textDecoration: 'none',
        fontWeight: 400
      }"
    >
      <i class="el-icon-arrow-left" />返回
    </a>
    <span
      :style="{
        margin: '0 10px',
        color: '#ccc'
      }"
      >|</span
    >
    <span class="title">{{ title }}</span>
    <div style="position: absolute; right: 15px">
      <slot name="rightButton" />
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      validator(v) {
        if (!v.trim()) {
          return false
        }
        return true
      }
    }
  }
}
</script>
<style scoped>
.title {
  flex: 1 1 auto;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #24262a;
}
</style>