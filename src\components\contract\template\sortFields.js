const getFileIndex = (files, fileId) => {
  for (var index in files) {
    const file = files[index]
    if (file.fileId === fileId) {
      return index
    }
  }
  //找不到对应的文件
  return 9999
}

const sortFields = (fields, pageFields, files) => {
  if (pageFields[0]?.coordX < 1) {
    return fields
  }
  fields.sort((a, b) => {
    const aPageField = pageFields.find(item => item.fieldId === a.id)
    const aFileIndex = getFileIndex(files, aPageField.fileId)

    const bPageField = pageFields.find(item => item.fieldId === b.id)
    const bFileIndex = getFileIndex(files, bPageField.fileId)
    if (aFileIndex === bFileIndex) {
      if (aPageField.pageNo === bPageField.pageNo) {
        if (Math.abs(aPageField.coordY - bPageField.coordY) < 24) {
          return aPageField.coordX < bPageField.coordX ? -1 : 1
        }
        return aPageField.coordY < bPageField.coordY ? -1 : 1
      }
      //页面在前，则排前
      return aPageField.pageNo < bPageField.pageNo ? -1 : 1
    }

    return aFileIndex < bFileIndex ? -1 : 1
  })
}

export default sortFields