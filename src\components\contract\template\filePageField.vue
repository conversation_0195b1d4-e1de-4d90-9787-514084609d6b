<template>
  <div
    class="pageField"
    :style="{
      position: 'absolute',
      top: `${pageField.coordY}px`,
      left: `${pageField.coordX}px`,
      background: '#fff'
    }"
    draggable
    @mousedown="mousedown"
    @mouseup="mouseup"
    @dragend="dragend"
    @dragstart="dragstart"
    @mouseenter="isMouseOver = true"
    @mouseleave="isMouseOver = false"
  >
    <div
      class="text-ellipsis"
      :style="{
        display: isActive ? '' : 'none',
        //必须是absolute，否则整个pageField就需要减去其高度，这里使用absolute避免
        position: 'absolute',
        top: `-19px`,
        fontSize: '12px',
        padding: '1px 5px',
        background: '#EEF0F4',
        maxWidth: `${pageField.width - 8}px`
      }"
    >
      {{ title }}
    </div>
    <span
      v-if="field.writeRequired && !isSignatureFieldType(field.type)"
      :style="{
        color: 'red',
        fontSize: `${pageField.fontSize}px`,
        position: 'absolute',
        left: `${-pageField.fontSize / 2 - 5}px`,
        top: `${pageField.height / 2 - pageField.fontSize / 2}px`
      }"
    >
      *
    </span>
    <div
      v-if="isSignatureFieldType(field.type)"
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'move',
        background: `${isActive ? bgColor + '50' : bgColor + '20'}`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        borderRadius: '4px',
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        resize: field.type === FieldTypeDate ? 'horizontal' : 'both'
      }"
    >
      <Person :pageField="pageField" v-if="field.type === FieldTypePerson" />
      <Company :pageField="pageField" v-if="field.type === FieldTypeCompany" />
      <DatePageField
        :pageField="pageField"
        v-if="field.type === FieldTypeDate"
      />
    </div>
    <div
      v-if="!isSignatureFieldType(field.type)"
      :id="elId"
      :style="{
        overflow: 'hidden',
        resize: isMultiLineText ? 'both' : 'horizontal',
        display: 'flex',
        // justifyContent: 'space-around',
        alignItems: 'flex-start',
        cursor: 'move',
        fontSize: `${pageField.fontSize}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${
          isNeedShowErrros ? '#F63939' : bgColor
        }`,
        background: `${isNeedShowErrros ? '#F63939' : bgColor}${
          isActive ? '50' : '20'
        }`,
        borderRadius: '4px'
      }"
    >
      <TextPageField :field="field" />
    </div>
    <div
      :style="{
        color: '#fff',
        borderRadius: '2px',
        fontSize: '12px',
        background: '#F63939',
        padding: '1px 5px',
        marginTop: '1px',
        position: 'absolute'
      }"
      v-if="isNeedShowErrros"
    >
      {{ pageFieldErrors }}
    </div>
    <i
      title="删除填充域"
      class="olading-iconfont oi-icon_delete_hover"
      v-if="isActive"
      @click="triggerDelete"
      :style="{
        color: 'red',
        fontSize: 16,
        position: 'absolute',
        right: '-8px',
        top: '-8px',
        cursor: 'pointer'
      }"
    />
  </div>
</template>
<script>
import { getColorByString } from './colors'
import TextPageField from './filePageField/text.vue'
import Company from './filePageField/company.vue'
import DatePageField from './filePageField/date.vue'
import Person from './filePageField/person.vue'
import {
  PageFieldDefaultWidth,
  PageFieldDefaultHeight
} from '../../../pages/contract/constants'
import isSignatureFieldType from './isSignatureFieldType'
import getPageFieldMaxLinesAndWordAmount from './getPageFieldMaxLinesAndWords'
import {
  FieldTypeDate,
  FieldTypePerson,
  FieldTypeCompany,
  WriteTypeMultiLineText
} from '../../../services/contract/constants'
import calcPageFieldMinHeight from './filePageField/calcMinHeight'
import calcPageFieldMinWidth from './filePageField/calcMinWidth'
var mouseX = 0
var mouseY = 0
var width = PageFieldDefaultWidth
var height = PageFieldDefaultHeight
//上次拖拽的PageField，它从原PageField clone出来，append到body，被移动到一个看不见的地方
var lastDragPageFieldEl = null
//上次拖拽的PageField, 因为拖拽时候，鼠标可以移动到其他pagefield上，导致触发了其他field的mouseup
//这里采用标记法 屏蔽掉这种行为
var lastMouseDownPageFieldId = ''
//标志位，因为多个字段共用了同一个值 所以当前字段未进行resize操作时候
//不应该重置size
var needResize = false
const resizeObserver = new ResizeObserver(entries => {
  const c = entries[0]
  //5是左侧的padding 2是上下边框
  width = c.contentRect.width + 5
  height = c.contentRect.height + 2
  needResize = true
})

const getPageFieldErrors = field => {
  var errors = []
  if (field.signStepId === '') {
    errors.push('未选择签署方')
  }
  if (field.name.trim() === '') {
    errors.push('名称不能为空')
  }

  return errors
}
export default {
  props: {
    field: Object,
    pageField: Object,
    currentScale: Number,
    focusPageFiled: Object,
    isSubmitting: Boolean
  },
  components: {
    TextPageField,
    Company,
    Person,
    DatePageField
  },
  data() {
    return {
      FieldTypeDate,
      FieldTypePerson,
      FieldTypeCompany,
      isMouseOver: false
    }
  },
  mounted() {
    var el = document.getElementById(this.elId)
    resizeObserver.observe(el)
  },
  computed: {
    elId() {
      return `pageField${this.pageField.id}`
    },
    isMultiLineText() {
      return this.field.writeType === WriteTypeMultiLineText
    },
    bgColor() {
      return getColorByString(this.field.signStepId)
    },
    title() {
      const pageField = this.pageField
      const field = this.field
      if (isSignatureFieldType(field.type)) {
        return field.signStepName ? `${field.signStepName} 填写` : ''
      }
      if (field.type === FieldTypeDate && field.signStepName) {
        return `${field.signStepName} 填写`
      }
      const [lines, words] = getPageFieldMaxLinesAndWordAmount(pageField)
      if (field.writeType === WriteTypeMultiLineText) {
        if (field.signStepName) {
          return `${field.signStepName} (可输入${
            lines ? lines : 1
          }行, ${words}字符)`
        } else {
          return `可输入${lines ? lines : 1}行, ${words}字符`
        }
      }

      if (!field.signStepName) {
        return `可输入${words}字符`
      }
      return `${field.signStepName} (可输入${words}字符)`
    },
    isNeedShowErrros() {
      if ((this.isSubmitting || this.isActive) && this.pageFieldErrors) {
        return true
      }
    },
    pageFieldErrors() {
      var errs = getPageFieldErrors(this.field)
      if (!errs.length) {
        return ''
      }

      return '控件字段设置不完整'
    },
    pageFieldMinHeight() {
      return calcPageFieldMinHeight(this.pageField, this.field)
    },
    pageFieldMinWidth() {
      return calcPageFieldMinWidth(this.pageField, this.field)
    },
    isValid() {
      return false
    },
    isActive() {
      if (this.isMouseOver) {
        return true
      }
      if (this.focusPageFiled) {
        return this.focusPageFiled.id === this.pageField.id
      }

      return
    }
  },
  methods: {
    isSignatureFieldType,
    mousedown(e) {
      lastMouseDownPageFieldId = this.pageField.id
      var rect = e.target.getBoundingClientRect()
      mouseX = e.clientX - rect.left //x position within the element.
      mouseY = e.clientY - rect.top //y position within the element.
      needResize = false
      this.$emit('focus', this.pageField)
    },
    mouseup(e) {
      if (!needResize) {
        return
      }
      if (lastMouseDownPageFieldId !== this.pageField.id) {
        return
      }

      this.$emit('change', this.pageField.id, {
        width,
        height
      })
      needResize = false
    },
    dragend() {
      const pageFieldEl = document.getElementById(this.elId)
      pageFieldEl.parentElement.style.display = ''
      if (lastDragPageFieldEl) {
        lastDragPageFieldEl.remove()
      }
    },
    dragstart(e) {
      e.dataTransfer.setData(
        'pageField',
        JSON.stringify({
          ...this.pageField,
          mouseX,
          mouseY
        })
      )

      const pageField = this.pageField
      const currentScale = this.currentScale

      const pageFieldEl = document.getElementById(this.elId)
      lastDragPageFieldEl = pageFieldEl.cloneNode(true)
      lastDragPageFieldEl.id = Math.random()
      lastDragPageFieldEl.style.position = 'absolute'
      lastDragPageFieldEl.style.left = '-1000px'
      lastDragPageFieldEl.style.zoom = `${currentScale}`
      if (!isSignatureFieldType(this.field.type)) {
        lastDragPageFieldEl.style.height = `${pageField.height}px`
        lastDragPageFieldEl.style.width = `${pageField.width}px`
      } else {
        const cel = lastDragPageFieldEl.children[0]
        const cw = parseInt(cel.style.width, 10)
        const ch = parseInt(cel.style.height, 10)
        cel.style.height = `${ch}px`
        cel.style.width = `${cw}px`
        cel.style.fontSize = `${pageField.fontSize}px`
        if (this.field.type === FieldTypeDate) {
          cel.style.fontSize = '12px'
        }
      }

      //必须 否则会偏大
      lastDragPageFieldEl.style.minWidth = ``
      lastDragPageFieldEl.style.minHeight = ``
      lastDragPageFieldEl.style.fontSize = `${pageField.fontSize}px`

      document.body.appendChild(lastDragPageFieldEl)
      //这里这么做是避免clone后的lastDragPageFieldEl 直接消失
      setTimeout(() => (pageFieldEl.parentElement.style.display = 'none'), 0)

      e.dataTransfer.setDragImage(lastDragPageFieldEl, mouseX, mouseY)
    },
    triggerDelete() {
      this.$emit('delete', this.pageField)
    }
  }
}
</script>