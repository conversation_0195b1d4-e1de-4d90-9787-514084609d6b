<template>
  <div
    :style="{
      display: 'flex'
    }"
    v-if="init"
  >
    <FieldContainer
      :getDragImage="getDragImage"
      :currentScale="$attrs.currentScale"
      :field="signature"
      :type="signature.type"
      v-for="signature in shownSignatures"
      :key="signature.type"
    >
      <div class="box">
        <i
          :class="signature.icon"
          style="font-size: 18px"
          v-if="signature.icon.includes('olading-iconfont')"
        />
        <img :src="signature.icon" height="24" width="24" v-else />
        <br />
        {{ signature.name }}
      </div>
    </FieldContainer>
    <div
      :style="{
        display: 'none'
      }"
    >
      <CompanyPageField ref="company" />
      <DatePageField ref="date" />
      <PersonPageField ref="person" />
    </div>
    <span v-if="!shownSignatures.length && this.filter">暂无搜索内容</span>
  </div>
</template>
<script>
import FieldContainer from './fieldContainer.vue'
import PersonPageField from '../filePageField/person.vue'
import CompanyPageField from '../filePageField/company.vue'
import DatePageField from '../filePageField/date.vue'
import personIcon from '../../../../assets/images/icon/<EMAIL>'
import companyIcon from '../../../../assets/images/icon/<EMAIL>'
import { FieldTypeDate } from '../../../../services/contract/constants'
export default {
  components: {
    FieldContainer,
    PersonPageField,
    CompanyPageField,
    DatePageField
  },
  computed: {
    shownSignatures() {
      const signatures = [
        {
          type: '1',
          name: '个人签名',
          icon: personIcon
        },
        {
          type: '2',
          name: '企业印章',
          icon: companyIcon
        },
        {
          type: '3',
          name: '签署日期',
          icon: 'olading-iconfont oi-icon_calendarrange'
        }
      ]
      var r = []
      for (var c of signatures) {
        if (this.isNeedShow(c.type) && this.hitFilter(c.name)) {
          r.push(c)
        }
      }

      return r
    }
  },
  methods: {
    getDragImage(type) {
      switch (type) {
        case 'person':
          return this.$refs.person
        case 'company':
          return this.$refs.company
        case 'date':
          return this.$refs.date

        default:
          throw new Error('not suported type')
      }
    },
    getPerson() {
      return this.$refs.person
    },
    getCompany() {
      return this.$refs.company
    },
    getDate() {
      return this.$refs.date
    },
    hitFilter(name) {
      if (!this.filter) {
        return true
      }

      return name.includes(this.filter)
    },
    isNeedShow(type) {
      for (var c of this.signatures) {
        if (c.signerType == type || type === FieldTypeDate) {
          return true
        }
      }

      return false
    }
  },
  mounted() {
    this.init = true
  },
  props: {
    signatures: Array,
    filter: String
  },
  data() {
    return {
      init: null
    }
  }
}
</script>
<style scoped>
.box {
  width: 68px;
  height: 58px;
  padding-top: 10px;
  border: 1px solid #cbced8;
  border-radius: 8px;
  text-align: center;
  overflow: hidden;
  margin-right: 8px;
  cursor: pointer;
}
</style>