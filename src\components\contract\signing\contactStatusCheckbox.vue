<template>
  <div
    v-if="statusList.length > 0"
    :style="{
      display: 'flex',
      alignItems: 'center',
      background: '#f7fafd',
      paddingLeft: '16px',
      paddingTop: '20px',
      minWidth: '1050px'
    }"
  >
    <template>
      <span
        :style="{
          marginRight: '12px',
          width: '96px',
          display: 'block',
          fontSize: '14px',
          textAlign: 'right'
        }"
        >筛选</span
      >
      <div style="display: flex">
        <el-checkbox-button
          style="margin-right: 8px; position: relative"
          v-model="checkAll"
          @change="handleCheckAllChange"
          ><div class="select-icon olading-iconfont">
            全部
            <i class="icon" v-if="checkAll">&#xe6c7;</i>
          </div></el-checkbox-button
        >
        <el-checkbox-group
          @change="contractStatusChange"
          v-on="$listeners"
          v-bind="$attrs"
        >
          <el-checkbox-button
            style="margin-right: 8px; position: relative"
            v-for="status in statusList"
            :label="status.value"
            :key="status.value"
            ><div class="select-icon olading-iconfont">
              {{ status.label }}
              <i class="icon" v-if="$attrs.value.includes(status.value)"
                >&#xe6c7;</i
              >
            </div></el-checkbox-button
          >
        </el-checkbox-group>
      </div>
    </template>
  </div>
</template>

<script>
import deepClone from '../../../helpers/deepClone'
export default {
  name: 'contactStatusCheckbox',
  props: ['group'],
  watch: {
    group: {
      handler(newValue) {
        if (!newValue) newValue = 'ALL'
        // 更新草稿状态
        this.statusList = this.originStatusList.filter(status =>
          status?.belong?.includes(newValue)
        )
      }
    }
  },
  data() {
    return {
      statusList: [
        {
          label: '待签署',
          value: '3',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ]
        },
        {
          label: '待填写',
          value: '2',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ]
        },
        {
          label: '待审核',
          value: '1',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ]
        },
        {
          label: '已完成',
          value: '6',
          belong: ['ALL', 'RECEIVED', 'CREATE_BY_ME', 'CARBON_TO_ME']
        },
        {
          label: '已撤回',
          value: '4',
          belong: ['ALL', 'RECEIVED', 'CREATE_BY_ME', 'CARBON_TO_ME']
        },
        {
          label: '已拒绝',
          value: '7',
          belong: ['ALL', 'RECEIVED', 'CREATE_BY_ME', 'CARBON_TO_ME']
        },
        {
          label: '已逾期',
          value: '5',
          belong: ['ALL', 'RECEIVED', 'CREATE_BY_ME', 'CARBON_TO_ME']
        },
        {
          label: '即将截止',
          value: '-999',
          belong: [
            'ALL',
            'HANDLE_BY_ME',
            'HANDLE_BY_OTHER',
            'RECEIVED',
            'CREATE_BY_ME',
            'CARBON_TO_ME'
          ]
        }
      ],
      originStatusList: [],
      checkAll: false
    }
  },
  created() {
    this.originStatusList = deepClone(this.statusList)
  },
  methods: {
    contractStatusChange(newValue, a) {
      if (this.statusList.length === newValue.length) {
        this.handleCheckAllChange(true)
      } else {
        this.checkAll = false
      }
    },
    handleCheckAllChange(value) {
      if (value) {
        this.checkAll = true
      } else {
        this.checkAll = false
      }
      this.$emit('input', [])
    }
  }
}
</script>

<style scoped>
::v-deep .el-checkbox-button .el-checkbox-button__inner {
  border-radius: 8px;
  border: 1px solid #cbced8;
}
::v-deep .el-checkbox-button.is-checked .el-checkbox-button__inner {
  background: #ffffff;
  color: var(--o-primary-color) !important;
  border: 1px solid var(--o-primary-color) !important;
  box-shadow: none;
  overflow: hidden;
}
::v-deep
  .el-checkbox-button.is-checked
  .el-checkbox-button__inner
  .olading-iconfont.icon::after {
  position: absolute;
  content: '&#xe6c7;';
}
::v-deep .el-checkbox-button.is-focus .el-checkbox-button__inner {
  border: 1px solid #cbced8;
}
.select-icon {
  position: relative;
  font-size: 12px;
}
.select-icon .icon {
  position: absolute;
  font-size: 20px;
  top: 2px;
  right: -24px;
}
::v-deep .el-checkbox-button__inner {
  padding: 9px 25px;
  transition: none;
}
</style>