{"_api_bill_item_update_": "hroBillItemUpdate", "_api_bill_sendEmail_": "hroBillSendEmail", "abandonCandidateApi": "hroCandidateAbandon", "accountsetCheckNameApi": "hroAccountsetCheckName", "addAccountantApi": "hroAccountsetAdd", "addBillListApi": "hroBillAdd", "addCalendarManagementApi": "calendarManagementAdd", "addCustomerApi": "hroCustomerAdd", "addCustomerContractApi": "hroCustomerContractAdd", "addInvoiceApi": "hroInvoiceAdd", "addInvoiceContentApi": "hroContentAdd", "addSupplierApi": "hroSupplierAdd", "addSupplierContractApi": "hroSupplierContractAdd", "approvalProcessListApi": "hroApprovalProcessList", "billConfirmApi": "hroBillConfirm", "billListApi": "hroPaymentOffsetList", "billOffsetApi": "hroBillOffset", "billRollBackApi": "h<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "billSubmitApi": "hroBillSubmit", "checkCodeApi": "hroSupplierCorporationCheckCode", "checkNameApi": "hroSupplierCheckName", "contractCheckNameApi": "hroCustomerContractCheckName", "contractRenewApi": "hroSupplierContractRenew", "corporationCheckNameApi": "hroCustomerCorporationCheckName", "customerContractStopApi": "hroCustomerContractStop", "detailApi": "hroPaymentDetail", "disableAccountantApi": "hroAccountsetDisable", "disableSupplierApi": "hroSupplierDisable", "enableAccountantApi": "hroAccountsetEnable", "enableSupplierApi": "hroSupplierEnable", "exportTemplateApi": "hroPaymentExportTemplate", "getAccountCustomerContractItemListApi": "hroCustomerContractAccountItems", "getAccountCustomerContractListApi": "hroCustomerContractAccountList", "getAccountantDetailApi": "hroAccountsetDetail", "getAccountantListApi": "hroAccountsetPage", "getAccountsetItemListApi": "hroAccountsetItemBillList", "getAccountsetListApi": "hroAccountsetList", "getBillDetailApi": "hroBillDetail", "getBillItemDetailApi": "hroBillBillItemDetail", "getBillItemListApi": "hroBillItemList", "getBillListApi": "hroBillList", "getCalendarManagementListApi": "calendarManagementGetList", "getCandidateDetailApi": "hroCandidateDetail", "getCustomerBasicDetailApi": "hroCustomerBasicDetail", "getCustomerContractDetailApi": "hroCustomerContractDetail", "getCustomerContractListApi": "hroCustomerContractPage", "getCustomerContractListApi2": "hroCustomerContractList", "getCustomerCorporationListApi": "hroCustomerCorporationList", "getCustomerCorporationPageApi": "hroCustomerCorporationPage", "getCustomerListApi": "hroCustomerList", "getCustomerOptionsApi": "hroCustomerOptions", "getEmployeeOtherFeeListApi": "hroBillEmployeeOtherFeeList", "getIncrementInfoApi": "hroIncrementInfo", "getMerchantApi": "hroMerchantMerchantGetMerchant", "getOneDayListApi": "calendarManagementGetOneDayList", "getPaymentListApi": "hroPaymentList", "getSupplierBasicDetailApi": "hroSupplierBasicDetail", "getSupplierContractDetailApi": "hroSupplierContractDetail", "getSupplierContractListApi": "hroSupplierContractList", "getSupplierCorporationListApi": "hroSupplierCorporationList", "getSupplierCorporationPageApi": "hroSupplierCorporationPage", "getSupplierListApi": "hroSupplierList", "importPaymentApi": "hroPaymentImportPayment", "invoiceBackApi": "hroInvoiceBack", "invoiceConfirmApi": "hroInvoiceConfirm", "invoiceDetailApi": "hroInvoiceDetail", "invoiceInvalidApi": "hroInvoiceInvalid", "invoiceListApi": "hroInvoicePage", "invoiceListApi2": "hroInvoicePage2", "invoiceMailApi": "hroInvoiceMail", "invoiceMakeApi": "hroInvoiceMake", "invoiceReceivedApi": "hroInvoiceReceived", "invoiceSendApi": "hroInvoiceSend", "invoiceUpdateApplyApi": "hroInvoiceUpdate", "itemExportApi": "hroBillItemExport", "offsetApi": "hroPaymentOffset", "stopSupplierContractApi": "hroSupplierContractStop", "updateAccountantApi": "hroAccountsetUpdate", "updateCalendarManagementApi": "calendarManagementUpdate", "updateContractManagerApi": "hroSupplierContractManagerUpdate", "updateCustomerApi": "hroCustomerUpdate", "updateCustomerContractApi": "hroCustomerContractUpdate", "updateCustomerContractManagerApi": "hroCustomerContractManagerUpdate", "updateCustomerStatusApi": "hroCustomerStatusUpdate", "updateStatusCalendarManagementApi": "calendarManagementUpdateStatus", "updateSupplierApi": "hroSupplierUpdate", "updateSupplierContractApi": "hroSupplierContractUpdate", "waitEntryCandidateApi": "hroCandidateWaitEntry"}