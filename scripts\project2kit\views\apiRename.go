package views

import (
	"path/filepath"

	"github.com/alading/scm/front/kit/scripts/project2kit/helpers"
	"github.com/alading/scm/front/kit/scripts/project2kit/views/transfer"
)

func APIsRename(viewsPath, service string) {
	helpers.WalkFile(viewsPath, func(path string) error {
		ext := filepath.Ext(path)
		if ext != ".vue" {
			return nil
		}
		transfer.TransferAPIs(path, service)
		return nil
	})
}
