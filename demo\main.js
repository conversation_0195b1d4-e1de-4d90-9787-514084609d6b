import Vue from 'vue'
import VueRouter from 'vue-router'
// import ElementUI from 'element-ui'
// import BusinessUI from '@olading/olading-business-ui'
import routes from './routes'
import 'kit/assets/iconfont/iconfont.css'
// import 'kit/assets/themes/element.ui.css'
// import '@olading/olading-business-ui/dist/olading-business-ui.css'
import 'vant/lib/index.css'
import './configs'
import { setTheme } from 'kit/assets/themes'
setTheme()
// import vant from 'vant'

import App from './app.vue'

// Vue.use(ElementUI)
// Vue.use(BusinessUI)
Vue.use(VueRouter)
// Vue.use(vant)
var loading = options => {
  console.log('loading no implement')
  return {
    close() {
      console.log('loading close no implement')
    }
  }
}
Vue.prototype.$loading = loading

const router = new VueRouter({
  mode: 'history',
  base: '/',
  routes
})

router.beforeEach((to, from, next) => {
  document.title = to.meta.title
  next()
})

//为了避免类型检查
const store = null
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
}).$mount()
