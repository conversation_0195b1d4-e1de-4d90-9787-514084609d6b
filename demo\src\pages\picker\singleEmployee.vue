<template>
  <SingleEmployee
    title="单选员工"
    :employees="employees"
    :selectedEmployee="selectedEmployee"
    @select="select"
    @unselect="unselect"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
  />
</template>

<script>
import SingleEmployee from 'kit/components/ui/picker/employee/single.vue'
import testEmployees from './testEmployees'
export default {
  components: {
    SingleEmployee
  },
  data() {
    return {
      employees: testEmployees,
      selectedEmployee: null
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedEmployee)
    },
    cancel() {
      this.selectedEmployee = null
      console.log('cancel', this.selectedEmployee)
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      this.selectedEmployee = null
    },
    select(v) {
      this.selectedEmployee = v
    }
  }
}
</script>