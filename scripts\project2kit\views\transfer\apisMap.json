{"FEE_TYPE": "expenseFeeTplTree", "addFeeStandardApi": "expenseFeeStandardAdd", "createBudgetApi": "expenseBudgetSchemeAdd", "disableRiskRuleApi": "expenseRiskRuleDisable", "enableRiskRuleApi": "expenseRiskRuleEnable", "getApprovalNodeListApi": "expensePaymentApprovalNodeList", "getBatchDetailApi": "expensePaymentBatchDetail", "getBatchListApi": "expensePaymentBatchList", "getBudgetReportDetailApi": "expenseBudgetReportDetail", "getBudgetReportOverviewApi": "expenseBudgetReportOverview", "getBudgetReportOverviewDetailApi": "expenseBudgetReportOverviewDetail", "getBudgetReportReceiptApi": "expenseBudgetReportReceipt", "getBudgetSchemeHistoryDetailApi": "expenseBudgetSchemeHistoryDetail", "getBudgetSchemeHistoryListApi": "expenseBudgetSchemeHistoryList", "getCityGroupListApi": "expenseCityGroupList", "getCityTreeApi": "expenseCityTree", "getConsumeApi": "expenseStatisticsExpenseConsume", "getControlFieldApi": "expenseFeeStandardGetControlField", "getDepartmentTreeApi": "expenseDepartmentTree", "getDeptRatioApi": "expenseStatisticsExpenseDeptRatio", "getDimensionListApi": "expenseBudgetPackageDimensionList", "getDocExtendItemTreeApi": "expenseDocExtendItemTree", "getDocExtendListApi": "expenseDocExtendList", "getDocumentExtendQueryItemTreeApi": "expenseDocList", "getExpenseRoleListApi": "expenseRoleList", "getFeeListApi": "expenseFeeStandardList", "getFeeStandardDimensionApi": "expenseFeeStandardDimension", "getFeeStandardexpenseEndDateFieldApi": "expenseFeeStandardExpenseEndDateField", "getFeeTepTreeApi": "expenseFeeTplTree", "getFeetDateFieldApi": "expenseFieldList", "getInvoiceDetailApi": "expenseInvoiceDetail", "getInvoiceExportApi": "expenseInvoiceExport", "getInvoiceListApi": "expenseInvoiceList", "getLegalListApi": "expenseLegalList", "getMerchantApi": "merchantMerchantGetMerchant", "getPayAmountApi": "expensePaymentGetPayAmount", "getPayDownloadTypeAPi": "expensePaymentGetPayDownloadType", "getPaymentAccountApi": "expensePayAccountPaymentAll", "getPaymentListApi": "expensePaymentList", "getReceiptApplyApi": "expenseReceiptApplyList", "getReceiptListAPi": "expensePaymentBatchReceiptList", "getReceiptLoanApi": "expenseReceiptLoanList", "getReceiptTplListApi": "expenseReceiptTplList", "getReceiptTplRatioApi": "expenseStatisticsExpenseReceiptTplRatio", "getReimburseDetailApi": "expenseStatisticsStaffReimburseDetail", "getReimburseListApi": "expenseStatisticsStaffReimburseList", "getReimburseReceiptListApi": "expenseStatisticsStaffReimburseReceiptList", "getReimburseSumInfoApi": "expenseStatisticsStaffReimburseSumInfo", "getReimbursementDetailApi": "expenseReceiptReimbursementDetail", "getReimbursementListApi": "expenseReceiptReimbursementList", "getRiskRuleDetailApi": "expenseRiskRuleDetail", "getRiskRuleListApi": "expenseRiskRuleList", "getSchemeDetailApi": "expenseBudgetSchemeDetail", "getSchemeListApi": "expenseBudgetSchemeList", "getStandardDetailApi": "expenseFeeStandardDetail", "getTypeRatioApi": "expenseStatisticsExpenseTypeRatio", "getUserDepListApi": "expenseUserDeptList", "getUserListApi": "expenseUserList", "getVersionApi": "expenseFeeStandardGetVersion", "imporTExcelApi": "expenseBudgetSchemeImport", "importBankResultFileApi": "expensePaymentImportBankResultFile", "importPayCheckResult": "expensePaymentImportPayCheckResult", "importPaymentDetail": "expensePaymentImportPaymentDetail", "receiptLoanHistoryApi": "expenseReceiptLoanHistory", "receiptLoanRemindsApi": "expenseReceiptLoanRemind", "receiptLoanRepaymentApi": "expenseReceiptLoanRepayment", "schemeCopyApi": "expenseBudgetSchemeCopy", "schemeDeleteApi": "expenseBudgetSchemeDelete", "schemePublish": "expenseBudgetSchemePublish", "setBudgetDraftApi": "expenseBudgetSchemeDraftSave", "setCancelApi": "expensePaymentCancel", "setConfirmFailApi": "expensePaymentConfirmFail", "setConfirmSuccessApi": "expensePaymentConfirmSuccess", "setFeeCopyApi": "expenseFeeStandardCopy", "setFeeDisableApi": "expenseFeeStandardDisable", "setFeeEnableApi": "expenseFeeStandardEnable", "setOfflinePayApi": "expensePaymentOfflinePay", "setRejectApi": "expensePaymentReject", "updateFeeUpdatedApi": "expenseFeeStandardUpdate", "updateRiskRuleDetailApi": "expenseRiskRuleUpdate"}