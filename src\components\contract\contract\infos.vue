<template>
  <div class="contractInfos" style="position: relative">
    <Signature
      style="position: absolute; top: 8px; right: 5px; transform: rotate(15deg)"
      :status="infos.status"
      :processStatus="infos.processStatus"
    />
    <div class="item">
      <div style="color: #a8acba">合同名称</div>
      <div class="text-ellipsis-2line" style="width: 150px" :title="infos.name">
        {{ infos.name }}
      </div>
    </div>
    <div class="item" v-if="infos.status">
      <div style="color: #a8acba">签署状态</div>
      <StatusWithDot :status="infos.status" />
    </div>
    <div class="item" v-if="infos.no">
      <div style="color: #a8acba">合同编号</div>
      <div class="text-ellipsis" :title="infos.no">{{ infos.no }}</div>
    </div>
    <div class="item" v-if="infos.contractType">
      <div style="color: #a8acba">合同类型</div>
      {{ infos.contractType }}
    </div>
    <div class="item" v-if="isNeedShowSignEndTime() && infos.signEndTime">
      <div style="color: #a8acba">签署截止日期</div>
      <span>
        <span
          :style="{
            color: willExpired(infos.signEndTime) ? 'red' : ''
          }"
          >{{ infos.signEndTime }}</span
        >
      </span>
      <div v-if="!infos.signEndTime">不限制</div>
      <Tag
        style="position: relative; left: 10px"
        v-if="
          infos.signEndTime &&
          willExpired(infos.signEndTime) &&
          isShowSignEndTag()
        "
        bgColor="#feebf0"
        color="#e3726f"
        status="即将截止"
      />
    </div>
    <div class="item" v-if="infos.creator">
      <div style="color: #a8acba">发起方</div>
      {{ infos.creator.legal.name }}({{ infos.creator.signer.name }})
    </div>
    <div class="item" v-if="infos.createTime">
      <div style="color: #a8acba">发起时间</div>
      {{ infos.createTime }}
    </div>
    <div
      class="item"
      v-if="infos.certifiedUserList && infos.certifiedUserList.length"
    >
      <div style="color: #a8acba">被证明人</div>
      <div
        class="text-ellipsis-2line"
        :title="infos.certifiedUserList | toString"
      >
        {{ infos.certifiedUserList | toString }}
      </div>
    </div>
    <div class="item" v-if="infos.certifiedUser">
      <div style="color: #a8acba">被证明人</div>
      <div class="text-ellipsis-2line" :title="infos.certifiedUser.name">
        {{ infos.certifiedUser.name }}
        <span v-if="infos.certifiedUser.mobile">
          ({{ infos.certifiedUser.mobile }})
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import {
  ContractStatusFilling,
  ContractStatusReviewing,
  ContractStatusSigning,
  ContractStatusOverdue,
  ContractStatusWithdrew,
  ContractStatusRejected
} from '../../../services/contract/constants'
import StatusWithDot from './statusWithDot.vue'
import Signature from './signature.vue'
import Tag from './tag.vue'
export default {
  components: {
    StatusWithDot,
    Signature,
    Tag
  },
  filters: {
    toString(users) {
      var r = []
      for (var c of users) {
        var s = `${c.name}`
        if (c.mobile) {
          s += ` (${c.mobile})`
        }
        r.push(s)
      }

      return r.join(', ')
    }
  },
  methods: {
    isNeedShowSignEndTime() {
      // 审批中，填写中，签署中 已逾期 已撤回 状态的合同才显示 “即将截止”
      if (this.infos.status === ContractStatusFilling) {
        return true
      }
      if (this.infos.status === ContractStatusReviewing) {
        return true
      }
      if (this.infos.status === ContractStatusSigning) {
        return true
      }
      if (this.infos.status === ContractStatusOverdue) {
        return true
      }
      if (this.infos.status === ContractStatusWithdrew) {
        return true
      }
      if (this.infos.status === ContractStatusRejected) {
        return true
      }

      return false
    },
    isShowSignEndTag() {
      //已拒绝、已撤回、已逾期，都不显示即将截止标志
      if (this.infos.status === ContractStatusRejected) {
        return false
      }
      if (this.infos.status === ContractStatusWithdrew) {
        return false
      }
      if (this.infos.status === ContractStatusOverdue) {
        return false
      }

      return true
    },
    willExpired(datetime) {
      if (!this.isNeedShowSignEndTime()) {
        return false
      }

      var n = new Date().getTime()
      var p = new Date(datetime).getTime()
      if ((p - n) / 3600000 < 72.0) {
        return true
      }
      return false
    }
  },
  props: {
    infos: Object
  }
}
</script>
<style scoped>
.contractInfos {
  font-size: 14px;
}
.contractInfos .item {
  margin-bottom: 16px;
}
</style>