<template>
  <el-dialog
    v-on="$listeners"
    v-bind="$attrs"
    title="提示"
    :close-on-click-modal="false"
  >
    <div :key="key" v-for="(msgs, key) in errors">
      {{ key }}:<br />
      <div
        :style="{
          background: '#F7FAFD',
          borderRadius: '8px',
          padding: '10px',
          marginBottom: '20px'
        }"
      >
        <div :key="msg" v-for="msg in msgs">
          {{ msg }}
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="$emit('update:visible', false)">
        我知道了
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  created() {
    console.log('errors', this.errors)
  },
  props: {
    errors: Object
  }
}
</script>