<template>
  <div
    class="pageField"
    :style="{
      position: 'absolute',
      top: `${pageField.coordY}px`,
      left: `${pageField.coordX}px`,
      background: '#fff'
    }"
    @mousedown="mousedown"
    @mouseenter="isMouseOver = true"
    @mouseleave="isMouseOver = false"
  >
    <div
      :style="{
        position: 'absolute',
        bottom: `${pageField.height}px`,
        display: isActive ? '' : 'none',
        fontSize: '12px',
        background: '#EEF0F4',
        maxWidth: `${pageField.width - 8}px`,
        maxHeight: '100px',
        overflowY: 'auto',
        zIndex: 999
      }"
      class="webkit-scrollbar"
    >
      {{ title }}
    </div>
    <span
      v-if="field.writeRequired && !isSignatureFieldType(field.type)"
      :style="{
        color: 'red',
        fontSize: `${pageField.fontSize}px`,
        position: 'absolute',
        left: `${-pageField.fontSize / 2 - 5}px`,
        top: `${pageField.height / 2 - pageField.fontSize / 2}px`
      }"
    >
      *
    </span>
    <div
      v-if="isSignatureFieldType(field.type)"
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        background: `${isActive ? bgColor : bgColor + '20'}`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        borderRadius: '4px',
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        cursor: 'not-allowed'
      }"
    >
      <Person :pageField="pageField" v-if="field.type === FieldTypePerson" />
      <Company :pageField="pageField" v-if="field.type === FieldTypeCompany" />
      <DatePageField
        :pageField="pageField"
        v-if="field.type === FieldTypeDate"
      />
    </div>
    <div
      v-if="!isSignatureFieldType(field.type)"
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        // justifyContent: 'space-around',
        alignItems: 'flex-start',
        cursor: isNeedInput ? 'pointer' : 'not-allowed',
        fontSize: `${pageField.fontSize}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        background: `${isActive ? bgColor + '70' : bgColor + '20'}`,
        borderRadius: '4px',
        color: color
      }"
    >
      <TextPageField :field="field" />
    </div>
  </div>
</template>
<script>
import { getColorByString } from '../template/colors'
import TextPageField from '../template/filePageField/text.vue'
import Company from '../template/filePageField/company.vue'
import DatePageField from '../template/filePageField/date.vue'
import Person from '../template/filePageField/person.vue'
import {
  FieldTypePerson,
  FieldTypeCompany,
  FieldTypeText,
  FieldTypeDate,
  WriteTypeMultiLineText
} from '../../../services/contract/constants'
import isSignatureFieldType from '../template/isSignatureFieldType'
import calcMinHeight from '../template/filePageField/calcMinHeight'
import calcMinWidth from '../template/filePageField/calcMinWidth'
import getPageFieldMaxLinesAndWordAmount from '../template/getPageFieldMaxLinesAndWords'
export default {
  props: {
    field: Object,
    pageField: Object,
    focusPageFiled: Object,
    //签署方关联的参与方人员列表
    stepSigners: Array
  },
  components: {
    TextPageField,
    Company,
    Person,
    DatePageField
  },
  data() {
    return {
      isMouseOver: false,
      FieldTypePerson,
      FieldTypeCompany,
      FieldTypeText,
      FieldTypeDate
    }
  },
  computed: {
    elId() {
      return `pageField${this.pageField.id}`
    },
    bgColor() {
      if (!this.isNeedInput) {
        return '#b9b9b9'
      }

      return getColorByString(this.field.signStepId)
    },
    color() {
      if (!this.isNeedInput) {
        return '#ccc'
      }

      return '#46485A'
    },
    isNeedInput() {
      return this.field.modifiable || this.field.isCustomControl
    },
    title() {
      // const pageField = this.pageField
      const field = this.field
      const stepName = field.signStepName
      const m = this.stepSigners.find(item => item.name === stepName)
      if (m) {
        var r = []
        for (var c of m.signerList) {
          if (c.legal && c.legal.name) {
            r.push(c.legal.name)
          } else {
            r.push(c.signer.name)
          }
        }

        var rs = r.join(', ')

        if (isSignatureFieldType(this.field.type)) {
          rs += ' (发起方不可填写)'
        }
        if (!isSignatureFieldType(this.field.type) && !this.isNeedInput) {
          rs += ' (发起方不可填写)'
        }
        if (
          !rs.includes('发起方不可填写') &&
          field.writeType === WriteTypeMultiLineText
        ) {
          const [_, words] = getPageFieldMaxLinesAndWordAmount(this.pageField)
          rs += `(${field.value.length}/${words})`
        }
        return rs
      }
    },
    pageFieldMinHeight() {
      return calcMinHeight(this.pageField, this.field)
    },
    pageFieldMinWidth() {
      return calcMinWidth(this.pageField, this.field)
    },
    isActive() {
      if (this.isMouseOver) {
        return true
      }
      if (this.focusPageFiled) {
        return this.focusPageFiled.id === this.pageField.id
      }

      return
    }
  },
  methods: {
    isSignatureFieldType,
    mousedown(e) {
      if (!isSignatureFieldType(this.field.type) && this.isNeedInput) {
        this.$emit('focus', this.pageField)
        return
      }
    }
  }
}
</script>