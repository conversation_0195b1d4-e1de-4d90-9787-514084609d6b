package main

import (
	"flag"

	"github.com/alading/scm/front/kit/scripts/project2kit/apis"
	"github.com/alading/scm/front/kit/scripts/project2kit/views"
)

var path string
var action string
var service string

func init() {
	flag.StringVar(&path, "path", "", "path of codes")
	flag.StringVar(&action, "action", "", "apisToClient, viewsAPIsRename")
	flag.StringVar(&service, "service", "", "hro, contract")
}
func main() {
	flag.Parse()
	if path == "" {
		println("path is empty")
		return
	}
	if action == "" {
		println("action is empty")
		return
	}
	switch action {
	case "viewsAPIsRename":
		views.APIsRename(path, service)
	case "apisToClient":
		apis.ToClient(path)
	// case "componentsRename":
	// 	componentsRename(path)
	default:
		flag.Usage()
	}
}
