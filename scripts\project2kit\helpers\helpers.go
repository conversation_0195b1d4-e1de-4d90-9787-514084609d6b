package helpers

import (
	"bufio"
	"os"
	"path/filepath"
	"strings"
)

func WalkFile(path string, fn func(f string) error) {
	err := filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		return fn(path)
	})
	if err != nil {
		panic(err)
	}
}

func ReadFileToLines(path string) []string {
	f, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()

	var lines []string
	scanner := bufio.NewScanner(f)
	scanner.Split(bufio.ScanLines)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	return lines
}

func parseExport(line string) string {
	// ${window.env.platform.api}
	//兼容hro项目
	return strings.ReplaceAll(line, "${window.env.platform.api}", "/api/merchant/platform/")
}
func ParseExports(lines []string) []string {
	// lines := readFileToLines(f)
	lastStart := 0
	start := 0
	end := 0
	exports := make([]string, 0)
	for index, c := range lines {
		if strings.Contains(c, "export const") {
			start = index
			if index > end {
				if lastStart > 0 {
					export := parseExport(strings.Join(lines[lastStart:start], "\n"))
					exports = append(exports, export)
				}
				lastStart = start
			}
		}

		end = index
	}

	if start != 0 && start == lastStart {
		export := parseExport(strings.Join(lines[lastStart:], "\n"))
		exports = append(exports, export)
	}

	return exports
}

func FindPrevMatchString(lines []string, start int, s string) (prevSteps int) {
	for i := start; i >= 0; i-- {
		if strings.Contains(lines[i], s) {
			return start - i
		}
	}

	return -1
}

func FindNexMatchString(lines []string, start int, s string) (nextSteps int) {
	for i := start; i < len(lines); i++ {
		if strings.Contains(lines[i], s) {
			return i - start
		}
	}

	return -1
}
