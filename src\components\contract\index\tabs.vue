<template>
  <div class="tabs">
    <div
      :style="{
        display: 'flex'
      }"
    >
      <div
        class="item"
        :key="key(item, index)"
        v-for="(item, index) in steps"
        @click="select(item, index)"
      >
        <slot name="name" v-bind="{ item, index, isActived: isActived(index) }">
          <div :class="['tab', isActived(index) ? 'actived' : '']">
            {{ name(item) }}
          </div>
        </slot>
      </div>
    </div>
    <slot
      v-bind="{ item: citem, index: cindex, isActived: isActived(cindex) }"
    />
  </div>
</template>
<script>
export default {
  props: {
    steps: Array,
    defaultActivedIndex: {
      type: Number,
      default() {
        return 0
      }
    }
  },
  created() {
    this.activedIndex = this.defaultActivedIndex
    this.citem = this.steps[this.defaultActivedIndex]
  },
  data() {
    return {
      citem: null,
      cindex: 0,
      activedIndex: 0
    }
  },
  methods: {
    select(item, index) {
      this.citem = item
      this.activedIndex = index
      this.$emit('select', index)
    },
    isActived(index) {
      return index === this.activedIndex
    },
    name(item) {
      if (item && item.name) {
        return item.name
      }

      return item
    },
    key(item, index) {
      if (item && item.key) {
        return item.key
      }

      return index
    }
  }
}
</script>
<style scoped>
.item {
  margin-right: 30px;
  cursor: pointer;
}
.tab {
  height: 26px;
  font-size: 14px;
}
.tab.actived {
  color: #4f71ff;
  border-bottom: 2px solid #4f71ff;
}
</style>