<template>
  <div>
    <el-upload
      class="upload-component"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :disabled="disabled"
      :data="uploadData"
      :multiple="false"
      :style="{ display: imageUrl ? 'none' : 'block' }"
      ref="upload"
      list-type="picture-card"
      :show-file-list="false"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <div
      v-if="imageUrl"
      class="preview-container"
      :class="{ disabled: disabled }"
      @click="reuploadImage"
    >
      <el-image :src="imageUrl" fit="contain" class="preview-image" />
    </div>
  </div>
</template>

<script>
import { showMessage } from 'kit/helpers/showMessage'
import handleError from 'kit/helpers/handleError'

export default {
  props: {
    uploadUrl: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    uploadHeaders: {
      type: Object,
      default: () => ({})
    },
    uploadData: {
      type: Object,
      default: () => ({})
    },
    fileList: {
      type: Array,
      default: () => []
    },
    maxSize: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      imageUrl: ''
    }
  },
  created() {
    this.imageUrl = this.fileList[0]
  },
  methods: {
    handleSuccess(response) {
      if (response.success) {
        this.imageUrl = response.data.url
        this.$emit('upload-success', response.data.url)
      } else {
        response.errorCode = Number(response.errorCode)
        handleError(response)
      }
    },
    beforeUpload(file) {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']

      const isImage = allowedTypes.includes(file.type)

      if (!isImage) {
        this.$refs.upload.clearFiles()
        return showMessage('只能上传jpg、jpeg或png格式图片文件', 'error')
      }
      const isLt2M = file.size / 1024 / 1024 < this.maxSize

      if (!isLt2M) {
        this.$refs.upload.clearFiles()
        return showMessage(`图片大小不能超过 ${this.maxSize}MB`, 'error')
      }

      return isImage && isLt2M
    },
    removeImage() {
      this.imageUrl = ''
      this.$emit('remove-image')
    },
    reuploadImage() {
      if (this.disabled) return
      this.$refs.upload.clearFiles()
      this.$nextTick(() => {
        this.$refs.upload.$refs['upload-inner'].$refs.input.click() // 触发选择文件的弹出框
      })
    }
  }
}
</script>

<style scoped>
.upload-component {
  display: inline-block;
}

.upload-component ::v-deep .el-upload {
  background: #f3f5f7;
}
.upload-component ::v-deep .el-upload--picture-card {
  border-style: solid;
  border-color: #cad0dbff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-container {
  width: 204px;
  height: 122px;
  border-radius: 6px;
  opacity: 1;
  border: 1px solid #cad0dbff;
  background: #ffffffff;
  overflow: hidden;
  position: relative;
}

.preview-container.disabled:hover::after {
  content: none;
}

.preview-container:hover::after {
  content: '重新上传';
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
  width: 100%;
  font-size: 14px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-family: 'PingFang SC';
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
}

.preview-image {
  width: 100%;
  height: 100%;
}
</style>
