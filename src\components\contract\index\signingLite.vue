<template>
  <div
    class="taskLite"
    style="
      display: flex;
      height: 50px;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      border-bottom: 1px solid #eef0f4;
    "
  >
    <div
      style="flex: 1 1 auto; font-weight: 500"
      class="text-ellipsis"
      :title="signing.name"
    >
      {{ signing.name }}
    </div>
    <div style="flex: 0 0 180px">
      <div v-if="waitedTime" style="font-weight: 400">{{ waitedTime }}</div>
      <span style="color: #777c94" v-if="signing.signEndTime">
        签署截止日期：{{ formatDateTime('yyyy-MM-dd', signing.signEndTime) }}
      </span>
    </div>
    <div style="flex: 0 0 286px" v-if="!showHandler">
      <div class="text-ellipsis" style="width: 286px" :title="sender">
        发起方：{{ sender }}
      </div>
      <span style="color: #777c94">
        提交时间：{{ formatDateTime('yyyy-MM-dd', signing.createTime) }}
      </span>
    </div>
    <div style="flex: 0 0 286px" v-if="showHandler">
      <div
        class="text-ellipsis"
        style="width: 286px; font-weight: 400"
        :title="handler"
      >
        待处理方：{{ handler }}
      </div>
      <span style="color: #85878b" v-if="handlerLegalName">
        {{ handlerLegalName }}
      </span>
    </div>
    <div style="flex: 0 0 100px">
      <el-button @click="$emit('buttonClick')" plain>{{
        buttonText
      }}</el-button>
    </div>
  </div>
</template>
<script>
import waitedTime from '../signing/waitedTime'
import formatDateTime from '../../../formatters/dateTime'
export default {
  created() {},
  computed: {
    sender() {
      if (this.signing.creator.legal) {
        return `${this.signing.creator.legal.name} (${this.signing.creator.signer.name})`
      }

      return this.signing.creator.signer.name
    },
    handler() {
      if (!this.signing.handler) {
        return '-'
      }
      return this.signing.handler.signer.name
    },
    handlerLegalName() {
      if (!this.signing.handler) {
        return ''
      }
      if (this.signing.handler.legal) {
        return this.signing.handler.legal.name
      }
      return ''
    },
    waitedTime() {
      return waitedTime(this.signing.waitSeconds)
    }
  },
  props: {
    signing: {
      type: Object,
      default() {
        return {}
      }
    },
    showHandler: Boolean,
    buttonText: {
      type: String,
      default() {
        return '签署'
      }
    }
  },
  methods: {
    formatDateTime
  }
}
</script>