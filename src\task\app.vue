<template>
  <div class="content">
    <router-view />
  </div>
</template>

<script>
import { wxProfile } from 'kit/helpers/wxProfile'

export default {
  data() {
    return {
      isFileSelecting: false,
      lastVisibilityChangeTime: 0
    }
  },
  computed: {},
  created() {
    this.setupGlobalVisibilityListener()
  },
  beforeDestroy() {
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
    }
  },
  methods: {
    setupGlobalVisibilityListener() {
      this.visibilityChangeHandler = async () => {
        const now = Date.now()

        // 防抖：如果距离上次触发时间太短，忽略
        if (now - this.lastVisibilityChangeTime < 1000) {
          return
        }
        this.lastVisibilityChangeTime = now

        if (!document.hidden) {
          console.log('页面重新可见，检查是否需要认证检查')

          // 检查当前路由是否需要认证检查
          const needAuthCheck = this.shouldCheckAuth()
          if (!needAuthCheck) {
            console.log('当前页面不需要认证检查')
            return
          }

          // 延迟执行认证检查，避免文件选择过程中的误触发
          setTimeout(async () => {
            if (document.hidden) {
              console.log('页面已隐藏，跳过认证检查')
              return
            }

            console.log('执行认证状态检查')
            const data = await wxProfile()
            console.log('认证状态检查结果===', data)

            if (!data.authStatus) {
              // 根据当前路由决定跳转目标
              const source = this.getAuthSource()
              this.$router.push({
                path: '/ocr',
                query: { source }
              })
            }
          }, 500) // 延迟500ms执行
        }
      }
      document.addEventListener('visibilitychange', this.visibilityChangeHandler)
    },

    shouldCheckAuth() {
      // 不需要认证检查的页面列表
      const noAuthPages = ['/login', '/ocr', '/ocrIdentify', '/faceAuth']
      return !noAuthPages.includes(this.$route.path)
    },

    getAuthSource() {
      // 根据当前路由返回合适的 source 参数
      const routeSourceMap = {
        '/myTask': 'wx-myTask',
        '/mine': 'wx-mine',
        '/delivery': 'delivery'
      }
      return routeSourceMap[this.$route.path] || 'task-app'
    }
  }
}
</script>

<style></style>
