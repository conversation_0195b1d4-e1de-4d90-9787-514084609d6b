<template>
  <div
    :style="{
      width: '100%',
      display: 'flex',
      alignItems: 'center'
    }"
  >
    <div
      v-if="withPrefix"
      :style="{
        width: '4px',
        height: '14px',
        display: 'inline-block',
        background: '#4F71FF',
        marginRight: '8px'
      }"
    ></div>
    <slot>
      <span class="title">{{ title }}</span>
    </slot>
  </div>
</template>
<script>
export default {
  props: {
    withPrefix: {
      type: Boolean,
      default() {
        return true
      }
    },
    title: {
      type: String,
      validitor(v) {
        if (!v.trim()) {
          return false
        }
        return true
      }
    }
  }
}
</script>
<style  scoped>
.title {
  height: 14px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #24262a;
  line-height: 14px;
  font-weight: 400;
  font-size: 14px;
}
</style>