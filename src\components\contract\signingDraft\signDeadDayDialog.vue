<template>
  <el-dialog
    title="签署截止日期"
    :visible.sync="visible"
    width="550px"
    :close-on-click-modal="false"
  >
    <div style="width: 480px; margin: 0 auto; line-height: 1.5">
      <p style="background: #f7fafd; border-radius: 8px; padding: 16px 12px">
        合同超过截止日期未签署完成，将无法继续签订，需要重新发起。系统会在到期前1天（系统默认）提醒参与人。
      </p>
      <div style="margin-top: 16px">
        <b>无限期：</b>合同无签署时限要求，发起后一直可签署
      </div>
      <div>
        <b>发起签约时设置：</b> 使用模板发起签约时，由发起方设置截止日期
      </div>
      <div>
        <b>发起签约后固定天数：</b> 发起后固定天数内还未签订完成，则自动过期
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">我知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SignDeadDayDialog',
  data() {
    return {
      visible: false
    }
  },
  methods: {
    open() {
      this.visible = true
    }
  }
}
</script>

<style></style>