//需要先从swagger处拿到 api-docs.js
import contractAPIJSON from './data/marketing.json'
import fs from 'fs'
const tplClient = `
class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  __APIS__
}

export default Client
`
const tplAPI = `
async __NAME__(options = {}) {
  const resource = '__URI__'
  return this.httpClient.request(resource, options)
}
`
function fUpperCase(s) {
  return s.charAt(0).toUpperCase() + s.slice(1)
}
const gAPINameFromURI = uri => {
  // /api/template/makeDetail
  const t = uri.split('/')
  var r = []
  var isFirst = true
  for (var index in t) {
    if (!t[index] || t[index] === 'api') {
      continue
    }
    if (isFirst) {
      r.push(t[index])
      isFirst = false
    } else {
      r.push(fUpperCase(t[index]))
    }
  }

  return r.join('')
}
const gClient = apiJSON => {
  var apis = []
  for (var uri in apiJSON.paths) {
    const name = gAPINameFromURI(uri)
    var api = tplAPI.replace('__NAME__', name)
    var api = api.replace('__URI__', uri)
    apis.push(api)
  }

  return tplClient.replace('__APIS__', apis.join(''))
}

fs.writeFileSync(
  'src/services/contract/client_generated.js',
  gClient(contractAPIJSON)
)

console.log('please check file src/services/contract/client_generated.js')
