import { delay } from 'kit/helpers/delay'
import Confirm from './confirm.vue'
import Vue from 'vue'

export const oConfirm = (content, title, config) => {
  const div = document.createElement('div')
  document.body.appendChild(div)

  new Vue({
    render(h) {
      const vNode = h(Confirm, {
        props: {
          title,
          content,
          ...config
        },
        on: {
          async visible(value) {
            if (value) return
            await delay(300)
            document.body.removeChild(vNode.elm)
          }
        }
      })
      return vNode
    }
  }).$mount(div)
}
