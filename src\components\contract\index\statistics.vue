<template>
  <div style="display: flex">
    <div class="number" @click="toSigning(ContractStatusReviewing)">
      <span class="count">{{ statistics.approvingCount }}</span>
      <span class="title">审核中</span>
    </div>
    <div class="number" @click="toSigning(ContractStatusFilling)">
      <span class="count">{{ statistics.writingCount }}</span>
      <span class="title">填写中</span>
    </div>
    <div class="number" @click="toSigning(ContractStatusSigning)">
      <span class="count">{{ statistics.signingCount }}</span>
      <span class="title">签署中</span>
    </div>
    <div class="number" @click="toSigning(ContractStatusCompleted)">
      <span class="count">{{ statistics.finishCount }}</span>
      <span class="title">已完成</span>
    </div>
  </div>
</template>
<script>
import {
  ContractStatusReviewing,
  ContractStatusFilling,
  ContractStatusSigning,
  ContractStatusCompleted
} from '../../../services/contract/constants'
export default {
  methods: {
    toSigning(status) {
      this.$router.push('/signings?contractStatus=' + status)
    }
  },
  props: {
    statistics: {
      type: Object,
      default() {
        return {
          approvingCount: 0,
          writingCount: 0,
          signingCount: 0,
          finishCount: 0
        }
      }
    }
  },
  data() {
    return {
      ContractStatusReviewing,
      ContractStatusFilling,
      ContractStatusSigning,
      ContractStatusCompleted
    }
  }
}
</script>
<style scoped>
.number {
  height: 72px;
  width: 72px;
  text-align: center;
  position: relative;
  top: 10px;
  margin-right: 6px;
  cursor: pointer;
}
.number .count {
  font-size: 20px;
  font-weight: 500;
  display: block;
}
</style>