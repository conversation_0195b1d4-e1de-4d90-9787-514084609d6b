package pages

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
)

func isOutParentDir(path, f, from string) bool {
	fullPath := fmt.Sprintf("%s/%s", filepath.Dir(f), from)
	dir := filepath.Dir(fullPath)

	if !strings.Contains(dir, path) {
		return true
	}

	return false
}

func Format(basePath, fpath, importLine string) (formattedImportLine string) {
	reg := regexp.MustCompile(`from ['"](.*?)['"]`)
	matches := reg.FindStringSubmatch(importLine)
	if len(matches) < 2 {
		panic("no match found")
	}

	if !isOutParentDir(basePath, fpath, matches[1]) {
		return importLine
	}

	formattedImportLine = formatHelpers(fpath, importLine)
	formattedImportLine = formatHandleError(fpath, formattedImportLine)
	formattedImportLine = formatComponents(fpath, formattedImportLine)
	formattedImportLine = formatServices(fpath, formattedImportLine)
	formattedImportLine = formatClientFactory(fpath, formattedImportLine)
	formattedImportLine = formatAssets(fpath, formattedImportLine)
	formattedImportLine = formatHandleSuccess(fpath, formattedImportLine)
	println(importLine)
	println(formattedImportLine)
	println()

	return formattedImportLine
}
