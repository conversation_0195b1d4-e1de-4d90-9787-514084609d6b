<template>
  <!-- 用于列表页 签署方展示 有状态 有弹窗 一维数组 -->
  <div>
    <div v-for="item in value.signerList" :key="item.id">
      <el-tooltip
        :disabled="!processsList.length > 0"
        popper-class="tps"
        :open-delay="500"
        effect="light"
      >
        <template #content>
          <div class="tooltip-content">
            <div :key="index" v-for="(processs, index) in processsList">
              <h5
                v-if="
                  (value.handlingBy && value.handlingBy.id) ===
                    processs.signer.signer.id &&
                  processs.status == ContractWriteProcessStatusWaitingWrite
                "
                style="
                  margin: 0;
                  margin-bottom: 12px;
                  font-weight: 600;
                  font-size: 14px;
                  color: #24262a;
                "
              >
                当前处理人
              </h5>

              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 6px;
                "
              >
                <!-- 名称 -->
                <span
                  class="text-ellipsis"
                  :title="
                    processs.signer.signerType === SingerTypePerson
                      ? `${processs.signer.signer.name}`
                      : `${processs.signer.legal.name} (${processs.signer.signer.name})`
                  "
                  style="
                    font-size: 12px;
                    color: #46485a;
                    display: block;
                    width: 200px;
                  "
                  >{{
                    processs.signer.signerType === SingerTypePerson
                      ? `${processs.signer.signer.name}`
                      : `${processs.signer.legal.name} (${processs.signer.signer.name})`
                  }}</span
                >
                <!-- 状态 -->
                <span
                  v-show="
                    writeStatusMap[processs.status] ||
                    signStatusMap[processs.status]
                  "
                  style="
                    width: 36px;
                    height: 16px;
                    background: #f7fafd;
                    border-radius: 10px;
                    display: inline-black;
                    color: #4f71ff;
                    padding: 3px 8px;
                    text-align: center;
                  "
                >
                  {{
                    processs.type === 'write'
                      ? writeStatusMap[processs.status]
                      : signStatusMap[processs.status]
                  }}
                </span>
              </div>
              <!-- 是否查看 -->
              <span
                style="
                  margin-right: 10px;
                  width: 46px;
                  height: 16px;
                  background: #f8f8f8;
                  border-radius: 10px;
                  display: inline-black;
                  color: #a8acba;
                  padding: 3px 8px;
                "
                >{{ processs.received ? '已查看' : '未查看' }}</span
              >
              <!-- 等待时长 -->
              <span
                v-if="processs.waitSeconds"
                style="font-size: 10px; color: #a8acba"
                >{{ milliSecondToHours(processs.waitSeconds) }}</span
              >
              <div
                v-if="index !== processsList.length - 1"
                style="
                  width: 246px;
                  height: 1px;
                  background: #f7fafd;
                  margin: 12px 0;
                "
              ></div>
            </div>
          </div>
        </template>
        <div style="display: flex">
          <i style="margin-right: 5px" v-if="processsList.length > 0">
            <span
              class="dot isHandle"
              v-if="isCurrentHandle(item.signer.id, item.signerType)"
            />

            <span class="dot noHandle" v-else></span>
          </i>
          <div
            class="text-ellipsis"
            style="margin-bottom: 5px; height: 14px"
            v-if="item.signerType === SingerTypePerson"
            :title="item.signer && item.signer.name"
          >
            {{ item.signer && item.signer.name }}
          </div>
          <div
            class="text-ellipsis"
            style="margin-bottom: 5px; height: 14px"
            :title="`${item.legal && item.legal.name} (${
              item.signer && item.signer.name
            })`"
            v-else
          >
            {{
              `${item.legal && item.legal.name} (${
                item.signer && item.signer.name
              })`
            }}
          </div>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
const signStatusMap = {
  0: '',
  1: '待发送',
  2: '待签',
  3: '已签'
}
const writeStatusMap = {
  0: '',
  1: '待发送',
  2: '待填',
  3: '已填'
}
import {
  SingerTypePerson,
  ContractWriteProcessStatusWaitingWrite
} from '../../../services/contract/constants'
export default {
  name: 'signProcessList',
  data() {
    return {
      writeStatusMap,
      signStatusMap,
      SingerTypePerson,
      ContractWriteProcessStatusWaitingWrite
    }
  },
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    processsList() {
      for (let writeProcess of this.value.writeProcessList || []) {
        writeProcess.type = 'write'
      }
      for (let signProcess of this.value.signProcessList || []) {
        signProcess.type = 'sign'
      }
      return [
        ...(this.value.writeProcessList || []),
        ...(this.value.signProcessList || [])
      ]
    }
  },
  methods: {
    isCurrentHandle(currentSignerId, signerType) {
      const currentHandleId = this.value.handlingBy?.id
      for (let process of this.processsList) {
        // 2 待签 待填
        if (
          process.status == ContractWriteProcessStatusWaitingWrite &&
          currentSignerId == process.signer.signer.id &&
          signerType == process.signer.signerType &&
          currentSignerId == currentHandleId
        ) {
          return true
        }
      }
    },
    milliSecondToHours(millisecond) {
      if (millisecond < 60) {
        return '已等待' + millisecond + '秒'
      }
      const hours = parseInt(millisecond / 3600, 10)
      const remainMins = parseInt((millisecond - hours * 3600) / 60, 10)
      const days = parseInt(hours / 24, 10)
      const remainHours = hours - days * 24
      var r = []
      if (days) {
        r.push(`${days}天`)
        if (remainHours) {
          r.push(`${remainHours}小时`)
        }
      } else {
        if (hours) {
          r.push(`${hours}小时`)
        }
      }

      if (remainMins) {
        r.push(`${remainMins}分钟`)
      }

      return '已等待' + r.join('')
    }
  }
}
</script>

<style scoped>
.dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}
.isHandle {
  background: #4f71ff;
}
.noHandle {
  background: #ccc;
}
</style>
<style>
.tps.el-tooltip__popper.is-light {
  background: #ffffff;
  border: 1px solid #f7fafd !important;
  box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.2);
  border-radius: 8px;
  padding: 16px;
  width: 272px !important;
}
</style>