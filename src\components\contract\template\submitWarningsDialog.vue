<template>
  <el-dialog
    v-on="$listeners"
    v-bind="$attrs"
    title="提示"
    :close-on-click-modal="false"
  >
    <div :key="key" v-for="(msgs, key) in warnings">
      {{ key }}:<br />
      <div
        :style="{
          background: '#F7FAFD',
          borderRadius: '8px',
          padding: '10px',
          marginBottom: '20px'
        }"
      >
        <div :key="msg" v-for="msg in msgs">
          {{ msg }}
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)"> 返回修改 </el-button>
      <el-button type="primary" @click="$emit('submit')"> 继续提交 </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  created() {
    console.log('warnings', this.warnings)
  },
  props: {
    open: <PERSON><PERSON><PERSON>,
    warnings: Object
  }
}
</script>