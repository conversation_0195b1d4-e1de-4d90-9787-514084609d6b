const fs = require('fs');
const path = require('path');
const directoryPath = 'src';
let unusedComponentCount = 0

const removeWhitespace = str => str.replace(/\s+/g, '');

function extractComponentsFromFileContent(fileContent) {
  const components = [];
  const regex = /components:\s*{[^}]*}/g;
  const matches = regex.exec(fileContent);
  if (matches) {
    const componentsObj = matches[0].replace('components:', '').trim();
    const componentsArray = componentsObj.slice(1, componentsObj.length - 1).split(',');
    for (const component of componentsArray) {
      const [componentName] = removeWhitespace(component).split(':');
      if(componentName) components.push(componentName.trim());
    }
  }
  return components;
}

function isComponentUsedInVueTemplate(fileContent, componentName) {
  const templateRegex = new RegExp(`<${componentName}[^>]*>`, 'g');
  const renderRegex = new RegExp(`h\\(${componentName}`);
  const classRegex = new RegExp(`${componentName}\\.`);
  const callbackRegex = new RegExp(`${componentName}\\(`);
 
  return (
    templateRegex.test(fileContent) ||
    renderRegex.test(fileContent) ||
    classRegex.test(fileContent) ||
    callbackRegex.test(fileContent)
  );
 }

function scanDirectory(directory) {
  const files = fs.readdirSync(directory);
  for (const file of files) {
    const filePath = path.join(directory, file);
    const fileStats = fs.statSync(filePath);
    if (fileStats.isDirectory()) {
      scanDirectory(filePath); // 递归扫描子目录
    } else if (file.endsWith('.vue')) {
      scanVueFile(filePath);
    }
  }
}

function scanVueFile(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  const components = extractComponentsFromFileContent(fileContent);
  for (const component of components) {
    if (!isComponentUsedInVueTemplate(fileContent, component)) {
      unusedComponentCount++
      console.log(`Component ${component} is not used in ${filePath}`);
    }
  }
}

scanDirectory(directoryPath);

console.log(unusedComponentCount ? '\n❌Please remove no used components!' : '✅All right, cool!')
