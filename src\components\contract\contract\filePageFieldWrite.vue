<template>
  <div
    class="pageField"
    :style="{
      position: 'absolute',
      top: `${pageField.coordY}px`,
      left: `${pageField.coordX}px`,
      background: '#fff'
    }"
    @mousedown="mousedown"
    @mouseenter="isMouseOver = true"
    @mouseleave="isMouseOver = false"
  >
    <span
      v-if="field.writeRequired && !isSignatureFieldType(field.type)"
      :style="{
        color: 'red',
        fontSize: `${pageField.fontSize}px`,
        position: 'absolute',
        left: `${-pageField.fontSize / 2 - 5}px`,
        top: `${pageField.height / 2 - pageField.fontSize / 2}px`
      }"
    >
      *
    </span>
    <div
      v-if="
        isSignatureFieldType(field.type) &&
        field.signStatus !== SignStatusSigned
      "
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        background: `${bgColor}20`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        borderRadius: '4px',
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        cursor: 'not-allowed'
      }"
    >
      <Person :pageField="pageField" v-if="field.type === FieldTypePerson" />
      <Company :pageField="pageField" v-if="field.type === FieldTypeCompany" />
      <DatePageField
        :pageField="pageField"
        v-if="field.type === FieldTypeDate"
      />
    </div>
    <div
      v-if="!isSignatureFieldType(field.type)"
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        // justifyContent: 'space-around',
        alignItems: 'flex-start',
        cursor: isNeedInput ? 'pointer' : 'not-allowed',
        fontSize: `${pageField.fontSize}px`,
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        minHeight: `${pageFieldMinHeight}px`,
        minWidth: `${pageFieldMinWidth}px`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        background: `${bgColor}20`,
        borderRadius: '4px',
        color: color
      }"
    >
      <TextPageField :field="field" />
    </div>
  </div>
</template>
<script>
import { getColorByString } from '../template/colors'
import TextPageField from '../template/filePageField/text.vue'
import Company from '../template/filePageField/company.vue'
import DatePageField from '../template/filePageField/date.vue'
import Person from '../template/filePageField/person.vue'
import { PageFieldDefaultHeight } from '../../../pages/contract/constants'
import {
  FieldTypePerson,
  FieldTypeCompany,
  FieldTypeText,
  FieldTypeDate,
  SignStatusSigned
} from '../../../services/contract/constants'
import isSignatureFieldType from '../template/isSignatureFieldType'
export default {
  props: {
    field: Object,
    pageField: Object,
    focusPageFiled: Object
  },
  components: {
    TextPageField,
    Company,
    Person,
    DatePageField
  },
  data() {
    return {
      isMouseOver: false,
      FieldTypePerson,
      FieldTypeCompany,
      FieldTypeText,
      FieldTypeDate,
      SignStatusSigned
    }
  },
  created() {
    console.log('stepSigners3', this.stepSigners)
  },
  computed: {
    elId() {
      return `pageField${this.pageField.id}`
    },
    bgColor() {
      if (!this.isNeedInput) {
        return '#b9b9b9'
      }

      return getColorByString(this.field.signStepId)
    },
    color() {
      if (!this.isNeedInput) {
        return '#ccc'
      }

      return '#46485A'
    },
    isNeedInput() {
      return this.field.writeable
    },
    pageFieldMinHeight() {
      if (isSignatureFieldType(this.field.type)) {
        return 64
      }
      return PageFieldDefaultHeight > this.pageField.fontSize
        ? PageFieldDefaultHeight
        : this.pageField.fontSize + 20
    },
    pageFieldMinWidth() {
      const fw = this.field.name.length * this.pageField.fontSize + 20

      if (this.field.type !== FieldTypeText) {
        if (this.field.type === FieldTypeDate) {
          return this.pageField.dateFormat.length * 12 + 10
        }
        return fw
      }

      return fw
    },
    isActive() {
      if (this.isMouseOver) {
        return true
      }
      if (this.focusPageFiled) {
        return this.focusPageFiled.id === this.pageField.id
      }

      return
    }
  },
  methods: {
    isSignatureFieldType,
    mousedown(e) {
      if (!isSignatureFieldType(this.field.type) && this.isNeedInput) {
        this.$emit('focus', this.pageField)
        return
      }
    }
  }
}
</script>