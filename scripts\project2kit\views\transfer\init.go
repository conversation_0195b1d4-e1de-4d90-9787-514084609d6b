package transfer

import (
	"encoding/json"
	"io/ioutil"
)

var platformAPIsMap map[string]string
var apisMap map[string]string

// getPreviewFileUrl getCaptchaURL
func init() {
	platformAPIsMap = map[string]string{}
	data, err := ioutil.ReadFile("platformAPIsMap.json")
	if err != nil {
		panic(err)
	}
	err = json.Unmarshal(data, &platformAPIsMap)
	if err != nil {
		panic(err)
	}

	apisMap = map[string]string{}

	data, err = ioutil.ReadFile("apisMap.json")
	if err != nil {
		panic(err)
	}
	err = json.Unmarshal(data, &apisMap)
	if err != nil {
		panic(err)
	}
}
