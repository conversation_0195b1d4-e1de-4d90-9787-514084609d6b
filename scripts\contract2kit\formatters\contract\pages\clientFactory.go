package pages

import "strings"

// 必须在formatServices之后调用
func formatClientFactory(f, line string) (formattedLine string) {
	if strings.Contains(line, "services/clientFactory") {
		formattedLine = strings.ReplaceAll(line, "services/clientFactory", "../services/contract/makeClient")
		formattedLine = strings.ReplaceAll(formattedLine, "{ makeContractClient }", "makeContractClient")
		return formattedLine
	}

	return line
}
