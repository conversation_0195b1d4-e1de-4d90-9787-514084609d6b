<template>
  <div class="signingContractWriter">
    <div style="display: flex">
      <div style="flex: 0 0 60px">
        <WriteProcessStatus :status="writer.status" />
      </div>
      <span
        style="flex: 1 1 auto; text-align: right; color: #ccc"
        v-if="writer.status !== ContractWriteProcessStatusWrote"
      >
        {{ waitingTime }}
      </span>
      <span style="flex: 1 1 auto; text-align: right; color: #ccc" v-else>
        {{ writer.wroteTime }}
      </span>
    </div>
    <div style="display: flex">
      <Icon :type="companyName ? SingerTypeCompany : SingerTypePerson" />
      <div style="flex: 1">
        <div v-if="companyName" style="font-weight: 400">
          {{ companyName }}
        </div>
        <span style="color: #777c94">
          <span
            :style="{
              color: companyName ? '' : '#46485A'
            }"
          >
            {{ writerName }}
          </span>
          ({{ writerMobile }})
        </span>
      </div>
      <span v-if="isShow" style="color: #a8acba">
        {{ writer.received ? '已查看' : '未查看' }}
      </span>
    </div>
  </div>
</template>
<script>
import Icon from '../../signingDraft/user/icon.vue'
import {
  SingerTypePerson,
  SingerTypeCompany,
  ContractWriteProcessStatusWrote
} from '../../../../services/contract/constants'
import WriteProcessStatus from '../../contract/writeProcessStatus.vue'
export default {
  components: {
    Icon,
    WriteProcessStatus
  },
  computed: {
    waitingTime() {
      const hours = parseInt(this.writer.waitSeconds / 3600, 10)
      const remainMins = parseInt(
        (this.writer.waitSeconds - hours * 3600) / 60,
        10
      )
      const days = parseInt(hours / 24, 10)
      const remainHours = hours - days * 24
      var r = []
      if (days) {
        r.push(`${days}天`)
        if (remainHours) {
          r.push(`${remainHours}小时`)
        }
      } else {
        if (hours) {
          r.push(`${hours}小时`)
        }
      }

      if (remainMins) {
        r.push(`${remainMins}分钟`)
      }
      if (!r.length) {
        return ''
      }
      return '已等待' + r.join('')
    },
    writerName() {
      return this.writer.signer.signer.name
    },
    writerMobile() {
      return this.writer.signer.signer.mobile
    },
    companyName() {
      if (this.writer.signer.legal) {
        return this.writer.signer.legal.name
      }
      return ''
    }
  },
  props: {
    isShow: {
      type: Boolean,
      default: () => true
    },
    writer: Object
  },
  data() {
    return {
      SingerTypePerson,
      SingerTypeCompany,
      ContractWriteProcessStatusWrote
    }
  }
}
</script>