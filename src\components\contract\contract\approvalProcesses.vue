<template>
  <div class="approvalProcesses">
    <div v-if="!steps || !steps.length">暂无流程</div>
    <StepsWithDashedLine :steps="steps">
      <template v-slot:icon="{ item }">
        <i
          class="olading-iconfont oi-icon_checked icon"
          v-if="item.status === ContractApprovalStatusPassed"
        />
        <i
          style="color: #f75154"
          class="olading-iconfont oi-icon_fail icon"
          v-else-if="item.status === ContractApprovalStatusRejected"
        />
        <img
          height="16"
          width="16"
          class="icon"
          v-else-if="item.status === ContractApprovalStatusReviewing"
          src="../../../assets/images/icon/<EMAIL>"
        />
        <i
          v-else
          style="color: #ccc"
          class="olading-iconfont oi-icon_unchecked icon"
        />
      </template>

      <template v-slot="{ item }">
        <div
          style="
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          "
        >
          <div style="font-weight: 400; font-size: 14px; margin-bottom: 5px">
            {{ item.title }}
          </div>
          <span v-if="item.approveTime" style="color: #a8acba">
            {{ item.approveTime }}
          </span>
          <span
            style="color: #b7b9bd"
            v-if="
              !item.createTime &&
              item.status === ContractApprovalStatusReviewing
            "
          >
            {{ waitingTime(item.waitSeconds) }}
          </span>
          <span v-if="item.createTime" style="color: #b7b9bd">
            {{ item.createTime }}
          </span>
        </div>

        <div v-if="item.company" style="display: flex">
          <Icon :type="SingerTypeCompany" />
          <div style="position: relative; left: -5px">
            <span>{{ item.company }}</span>
            <div style="color: #777c94; margin-top: 5px">
              {{ item.user }} ({{ item.mobile }})
            </div>
          </div>
        </div>
        <div v-else style="color: #777c94; margin-bottom: 5px">
          {{ item.user }} ({{ item.mobile }})
        </div>
        <div
          style="
            background: #f8f8f8;
            color: #777c94;
            padding: 8px 5px;
            border-radius: 8px;
          "
          v-if="item.remark"
        >
          {{ item.remark }}
        </div>
      </template>
    </StepsWithDashedLine>
  </div>
</template>
<script>
import StepsWithDashedLine from '../stepsWithDashedLine.vue'
import approvalStatus2string from '../../../services/contract/approvalStatus2string'
import {
  ContractApprovalStatusPassed,
  ContractApprovalStatusReviewing,
  ContractApprovalStatusRejected,
  ContractApprovalStatusWaitingSend,
  SingerTypeCompany
} from '../../../services/contract/constants'
import Icon from '../signingDraft/user/icon.vue'
export default {
  components: {
    StepsWithDashedLine,
    Icon
  },
  props: {
    createTime: String,
    creator: Object,
    processes: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      ContractApprovalStatusPassed,
      ContractApprovalStatusReviewing,
      ContractApprovalStatusRejected,
      ContractApprovalStatusWaitingSend,
      SingerTypeCompany
    }
  },
  computed: {
    steps() {
      var r = []
      if (!this.processes) {
        return r
      }
      r.push({
        title: '发起签署',
        createTime: this.createTime,
        company: this.creator.legal.name,
        user: this.creator.signer.name,
        mobile: this.creator.signer.mobile,
        status: ContractApprovalStatusPassed
      })

      for (var c of this.processes) {
        r.push({
          title: approvalStatus2string(c.status),
          approveTime: c.approveTime,
          user: c.approver.name,
          mobile: c.approver.mobile,
          remark: c.remark,
          status: c.status,
          waitSeconds: c.waitSeconds
        })
      }

      return r
    }
  },
  methods: {
    approvalStatus2string,
    waitingTime(waitSeconds) {
      const hours = parseInt(waitSeconds / 3600, 10)
      const remainMins = parseInt((waitSeconds - hours * 3600) / 60, 10)
      const days = parseInt(hours / 24, 10)
      const remainHours = hours - days * 24
      var r = []
      if (days) {
        r.push(`${days}天`)
        if (remainHours) {
          r.push(`${remainHours}小时`)
        }
      } else {
        if (hours) {
          r.push(`${hours}小时`)
        }
      }

      if (remainMins) {
        r.push(`${remainMins}分钟`)
      }

      return '已等待' + r.join('')
    }
  }
}
</script>
<style scoped>
.icon {
  background: #fff;
  position: relative;
  top: -8px;
  left: 0px;
  color: #5173fe;
}
</style>