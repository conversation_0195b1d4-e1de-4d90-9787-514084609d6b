<template>
  <div class="signingContractWriter">
    <div style="display: flex">
      <div style="flex: 0 0 60px">
        <SignProcessStatus :status="signer.status" />
      </div>
      <span
        style="flex: 1 1 auto; text-align: right; color: #a8acba"
        v-if="signer.status !== ContractSignProcessStatusSigned"
      >
        {{ waitingTime }}
      </span>
      <span style="flex: 1 1 auto; text-align: right; color: #a8acba" v-else>
        {{ signer.signedTime }}
      </span>
    </div>
    <div style="display: flex">
      <Icon :type="companyName ? SingerTypeCompany : SingerTypePerson" />
      <div style="flex: 1">
        <div v-if="companyName" style="font-weight: 400">
          {{ companyName }}
        </div>
        <span style="color: #777c94">
          <span
            :style="{
              color: companyName ? '' : '#46485A'
            }"
          >
            {{ signerName }}
          </span>
          ({{ signerMobile }})
        </span>
      </div>
      <span v-if="isShow" style="color: #a8acba">
        {{ signer.received ? '已查看' : '未查看' }}
      </span>
    </div>
  </div>
</template>
<script>
import Icon from '../../signingDraft/user/icon.vue'
import {
  SingerTypePerson,
  SingerTypeCompany,
  ContractSignProcessStatusSigned
} from '../../../../services/contract/constants'
import SignProcessStatus from '../../contract/signProcessStatus.vue'
export default {
  components: {
    Icon,
    SignProcessStatus
  },
  computed: {
    waitingTime() {
      const hours = parseInt(this.signer.waitSeconds / 3600, 10)
      const remainMins = parseInt(
        (this.signer.waitSeconds - hours * 3600) / 60,
        10
      )
      const days = parseInt(hours / 24, 10)
      const remainHours = hours - days * 24
      var r = []
      if (days) {
        r.push(`${days}天`)
        if (remainHours) {
          r.push(`${remainHours}小时`)
        }
      } else {
        if (hours) {
          r.push(`${hours}小时`)
        }
      }

      if (remainMins) {
        r.push(`${remainMins}分钟`)
      }
      if (!r.length) {
        return ''
      }
      return '已等待' + r.join('')
    },
    signerName() {
      return this.signer.signer.signer.name
    },
    signerMobile() {
      return this.signer.signer.signer.mobile
    },
    companyName() {
      if (this.signer.signer.legal) {
        return this.signer.signer.legal.name
      }
      return ''
    }
  },
  props: {
    isShow: {
      type: Boolean,
      default: () => true
    },
    signer: Object
  },
  data() {
    return {
      ContractSignProcessStatusSigned,
      SingerTypePerson,
      SingerTypeCompany
    }
  }
}
</script>