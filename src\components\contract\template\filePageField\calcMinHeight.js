import isSignatureFieldType from '../isSignatureFieldType'
import { PageFieldDefaultHeight } from '../../../../pages/contract/constants'
import { FieldTypeDate } from '../../../../services/contract/constants'
const calcPageFieldMinHeight = (pageField, field) => {
  if (isSignatureFieldType(field.type) && field.type !== FieldTypeDate) {
    return 64
  }

  if (PageFieldDefaultHeight > pageField.fontSize) {
    return PageFieldDefaultHeight
  }

  return pageField.fontSize + 2
}

export default calcPageFieldMinHeight
