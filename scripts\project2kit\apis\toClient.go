package apis

import (
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"strings"

	"github.com/alading/scm/front/kit/scripts/project2kit/helpers"
)

const tplClient = `
class Client {
	constructor(httpClient) {
	  if (!httpClient) {
		throw new Error('httpClient is required')
	  }
  
	  this.httpClient = httpClient
	}
	__APIS__
  }
  
  export default Client
  `

const tplAPI = `
  async __NAME__(options = {}) {
	const resource = '__URI__'
	__GET__
	return this.httpClient.request(resource, options)
  }
`

func ToClient(apisPath string) {
	platformAPIs := make([]string, 0)
	platformAPIsMap := make(map[string]string)

	apis := make([]string, 0)
	apisMap := make(map[string]string)

	noMatchedExports := make([]string, 0)

	helpers.WalkFile(apisPath, func(path string) error {
		lines := helpers.ReadFileToLines(path)
		exports := helpers.ParseExports(lines)
		matched, noMatched := toMap(path, exports)
		for key, resourceAndAPIName := range matched {
			resourceName, apiName, method := resourceAndAPIName[0], resourceAndAPIName[1], resourceAndAPIName[2]
			api := strings.ReplaceAll(tplAPI, "__NAME__", apiName)
			api = strings.ReplaceAll(api, "__URI__", resourceName)
			if method == "get" {
				api = strings.ReplaceAll(api, "__GET__", "options.method = \"GET\"")
			} else {
				api = strings.ReplaceAll(api, "__GET__", "")
			}

			if strings.Contains(resourceAndAPIName[0], "/api/merchant/platform") {
				platformAPIs = append(platformAPIs, api)
				platformAPIsMap[key] = resourceAndAPIName[1]
				continue
			}

			apisMap[key] = resourceAndAPIName[1]
			apis = append(apis, api)
		}

		for f, exports := range noMatched {
			noMatchedExports = append(noMatchedExports, f)
			noMatchedExports = append(noMatchedExports, exports...)
		}

		return nil
	})

	client := strings.ReplaceAll(tplClient, "__APIS__", strings.Join(apis, "\n"))

	dir := filepath.Dir(apisPath)

	ioutil.WriteFile(dir+"/client.js", []byte(client), 0644)

	data, _ := json.MarshalIndent(apisMap, "", "  ")
	ioutil.WriteFile(dir+"/apisMap.json", data, 0644)

	data = []byte(strings.Join(platformAPIs, "\n"))
	ioutil.WriteFile(dir+"/platformAPIs.js", data, 0644)

	data, _ = json.MarshalIndent(platformAPIsMap, "", "  ")
	ioutil.WriteFile(dir+"/platformAPIsMap.json", data, 0644)

	ioutil.WriteFile(dir+"/noMatchedExports.txt",
		[]byte(strings.Join(noMatchedExports, "\n")), 0644)
}
