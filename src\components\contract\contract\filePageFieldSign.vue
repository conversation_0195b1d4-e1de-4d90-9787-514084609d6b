<template>
  <div
    class="pageField"
    :style="{
      position: 'absolute',
      top: `${pageField.coordY}px`,
      left: `${pageField.coordX}px`,
      background: isNeedBackground ? '#fff' : ''
    }"
    @mouseenter="isMouseOver = true"
    @mouseleave="isMouseOver = false"
  >
    <div v-if="field.value">
      <Person
        :value="field.value"
        :pageField="pageField"
        v-if="field.type === FieldTypePerson"
      />
      <Company
        :value="field.value"
        :pageField="pageField"
        v-if="field.type === FieldTypeCompany"
      />
      <DatePageField
        :value="field.value"
        :pageField="pageField"
        v-if="field.type === FieldTypeDate"
      />
    </div>
    <div
      :style="{
        position: 'absolute',
        bottom: `${pageField.height}px`,
        display: isActive ? '' : 'none',
        fontSize: '12px',
        background: '#EEF0F4',
        maxWidth: `${pageField.width - 8}px`
      }"
    >
      {{ title }}
    </div>
    <div
      v-if="field.signStatus === SignStatusSigned"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '4px',
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        cursor: `${isNeedInput ? 'pointer' : 'not-allowed'}`
      }"
    ></div>
    <div
      v-if="!field.value && field.signStatus !== SignStatusSigned"
      :id="elId"
      :style="{
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        background: `${bgColor}20`,
        boxSizing: 'border-box',
        border: `1px ${isActive ? 'solid' : 'dashed'} ${bgColor}`,
        borderRadius: '4px',
        height: `${pageField.height}px`,
        width: `${pageField.width}px`,
        cursor: `${isNeedInput ? 'pointer' : 'not-allowed'}`
      }"
    >
      <Person :pageField="pageField" v-if="field.type === FieldTypePerson" />
      <Company :pageField="pageField" v-if="field.type === FieldTypeCompany" />
      <DatePageField
        :pageField="pageField"
        v-if="field.type === FieldTypeDate"
      />
    </div>
  </div>
</template>
<script>
import { getColorByString } from '../template/colors'
import Company from '../template/filePageField/company.vue'
import DatePageField from '../template/filePageField/date.vue'
import Person from '../template/filePageField/person.vue'
import {
  FieldTypePerson,
  FieldTypeCompany,
  FieldTypeText,
  FieldTypeDate,
  SignStatusSigning,
  SignStatusSigned
} from '../../../services/contract/constants'
export default {
  props: {
    field: Object,
    pageField: Object,
    //所有签署人
    signers: Array
  },
  components: {
    Company,
    Person,
    DatePageField
  },
  data() {
    return {
      isMouseOver: false,
      FieldTypePerson,
      FieldTypeCompany,
      FieldTypeText,
      FieldTypeDate,
      SignStatusSigned
    }
  },
  created() {
    console.log('stepSigners3', this.stepSigners)
  },
  computed: {
    isNeedBackground() {
      return this.field.signStatus !== SignStatusSigned
    },
    elId() {
      return `pageField${this.pageField.id}`
    },
    bgColor() {
      if (!this.isNeedInput) {
        return '#b9b9b9'
      }

      return getColorByString(this.field.signStepId)
    },
    color() {
      if (!this.isNeedInput) {
        return '#ccc'
      }

      return '#46485A'
    },
    isNeedInput() {
      return this.field.signStatus === SignStatusSigning
    },
    title() {
      const field = this.field
      const signer = this.signers.find(
        item => item.signerType === field.signerType
      )
      if (!signer) {
        return ''
      }
      if (signer.legal) {
        return signer.legal.name
      }

      return signer.signer.name
    },
    isActive() {
      if (this.isMouseOver) {
        return true
      }

      return false
    }
  }
}
</script>