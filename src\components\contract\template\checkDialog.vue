<template>
  <el-dialog
    :close-on-click-modal="false"
    :show-close="true"
    v-on="$listeners"
    v-bind="$attrs"
    width="30%"
    title="提示"
  >
    <div>
      <h3 style="color: #24262a; font-weight: 600; font-size: 14px">
        <i
          style="
            color: #e59b00;
            font-size: 16px;
            margin-right: 6px;
            vertical-align: middle;
          "
          class="el-icon-warning"
        >
        </i>
        合同类型信息不完整，请补充后启用
      </h3>
      <p style="color: #777c94; font-size: 12px">
        发现{{ checkEnable.name }}使用的以下信息不完整
      </p>
      <ul
        style="
          background-color: #f6fafd;
          padding: 20px 0 20px 40px;
          font-size: 12px;
        "
      >
        <li :key="index" v-for="(message, index) in checkEnable.message">
          {{ message }}
        </li>
      </ul>
    </div>
    <slot />
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="$emit('update:visible', false)"
        >我知道了</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    checkEnable: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>