<template>
  <div
    class="draggableV2"
    :style="{
      fontSize: '12px',
      marginBottom: '10px'
    }"
  >
    <div
      v-if="columns.length"
      :style="{
        display: 'flex',
        background: '#F7FAFD'
      }"
    >
      <div
        v-if="allowDrag || showIndex"
        :style="{
          height: '48px',
          lineHeight: '48px',
          textAlign: 'center',
          minWidth: '52px',
          flex: '0 0 52px'
        }"
      >
        {{ !allowDrag && showIndex ? '序号' : '' }}
      </div>
      <div
        :key="index"
        v-for="(item, index) in columns"
        :style="{
          height: '48px',
          lineHeight: '48px',
          background: '#F7FAFD',
          flex: item.width === 'auto' ? `1 1 auto` : `0 0 ${item.width}`
        }"
      >
        {{ item.label }}
      </div>
    </div>
    <!-- 头部end -->
    <div
      v-if="!value || !value.length"
      :style="{
        textAlign: 'center',
        padding: '50px 0',
        color: '#A8ACBA'
      }"
    >
      {{ emptyMessage }}
    </div>
    <div
      :style="{
        display: 'flex',
        flexDirection: 'column',
        flexWrap: 'nowrap'
      }"
      class="draggableItemsBox"
    >
      <div
        class="draggableItem"
        @dragover="dragover"
        @dragenter="dragenter($event, index)"
        :index="index"
        :style="{
          display: 'flex',
          order: index,
          alignItems: 'center'
        }"
        :tt="item.key"
        :key="item.key ? item.key : JSON.stringify(item)"
        v-for="(item, index) in value"
      >
        <div
          :style="{
            flex: '0 0 52px',
            height: '60px',
            lineHeight: '60px',
            textAlign: 'center'
          }"
          v-if="!allowDrag && showIndex"
        >
          {{ startIndex + index + 1 }}
        </div>
        <!-- 占位 -->
        <div
          :style="{
            width: '52px',
            height: '60px',
            lineHeight: '60px',
            textAlign: 'center'
          }"
          v-if="allowDrag && item.dragDisable === true"
        ></div>
        <!-- 拖动图标 -->
        <div
          v-if="allowDrag && item.dragDisable !== true"
          :style="{
            width: '52px',
            height: '60px',
            lineHeight: '60px',
            cursor: 'move',
            textAlign: 'center'
          }"
          draggable
          @dragstart="dragstart($event, index)"
          @dragend="dragend($event, index)"
        >
          <i
            class="olading-iconfont oi-icon_drag"
          />
        </div>
        <div style="flex: 1" ref="slot">
          <slot v-bind="{ item, index }"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
var dragStartIndex = -1
var dragStartEl = null
var lastEl = null
const findDraggableItem = el => {
  if (el.className.includes('draggableItem')) {
    return el
  }
  return findDraggableItem(el.parentElement)
}
const findDraggableItemsBox = el => {
  if (el.className.includes('draggableItemsBox')) {
    return el
  }
  return findDraggableItemsBox(el.parentElement)
}
export default {
  watch: {
    value: {
      handler(v) {
        const _this = this
        setTimeout(() => {
          _this.resetNodesWidth()
        }, 0)
      },
      deep: true
    }
  },
  props: {
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    value: {
      type: Array,
      default() {
        return [1, 2, 3, 5]
      }
    },
    startIndex: {
      type: Number,
      default() {
        return 0
      }
    },
    showIndex: {
      type: Boolean,
      default() {
        return false
      }
    },
    allowDrag: {
      type: Boolean,
      default() {
        return true
      }
    },
    emptyMessage: {
      type: String,
      default() {
        return '暂无数据'
      }
    }
  },
  mounted() {
    this.resetNodesWidth()
  },
  methods: {
    dragover(e) {
      e.preventDefault()
    },
    dragenter(e, cindex) {
      if (cindex === dragStartIndex) {
        return
      }

      const cel = e.currentTarget

      if (this.value[cindex].dragDisable) {
        return
      }

      const corder = cel.style.order
      cel.style.order = dragStartEl.style.order
      dragStartEl.style.order = corder
    },
    dragend(e, index) {
      if (lastEl) {
        lastEl.remove()
      }

      dragStartEl.style.opacity = ''

      const boxEl = findDraggableItemsBox(e.target)
      var orders = []
      for (var c of boxEl.children) {
        orders.push(parseInt(c.style.order, 10))
      }

      var t = [...this.value]
      var n = []
      for (var i = 0; i < t.length; i++) {
        const index = orders.findIndex(item => item === i)
        n[i] = t[index]
      }

      this.$emit('input', n)
    },
    dragstart(e, index) {
      const el = findDraggableItem(e.target)
      dragStartIndex = index
      dragStartEl = el
      lastEl = el.cloneNode(true)
      lastEl.id = Math.random()
      lastEl.style.position = 'absolute'
      lastEl.style.background = '#fff'
      lastEl.style.width = `${dragStartEl.clientWidth}px`
      lastEl.style.left = '-10000px'
      document.body.appendChild(lastEl)
      e.dataTransfer.setDragImage(lastEl, e.target.offsetX, e.target.offsetY)
      el.style.opacity = '0.6'
    },
    resetNodesWidth() {
      if (
        this.$refs.slot &&
        this.$refs.slot.length &&
        this.columns &&
        this.columns.length
      ) {
        for (var el of this.$refs.slot) {
          el.style.display = 'flex'
          el.style.flex = '1 1 auto'
          el.style.alignItems = 'center'

          for (var i = 0; i < el.children.length; i++) {
            const c = el.children[i]
            const w = this.columns[i]
            c.style.flex = w.width === 'auto' ? `1 1 auto` : `0 0 ${w.width}`
          }
        }
      }
    }
  }
}
</script>