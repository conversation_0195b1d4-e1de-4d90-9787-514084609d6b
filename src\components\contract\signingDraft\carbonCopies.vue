<template>
  <div class="carbinCopies">
    <CarbonCopyUser
      :user="item"
      :key="index"
      v-for="(item, index) in carbonCopies"
    />
    <a
      ref="hide"
      style="display: none; color: #4f71ff; cursor: pointer"
      @click="hide"
      >收起</a
    >
    <a
      ref="show"
      style="display: none; color: #4f71ff; cursor: pointer"
      @click="show"
      >显示全部</a
    >
  </div>
</template>
<script>
import CarbonCopyUser from './user/carbonCopy.vue'
var users = []
export default {
  components: {
    CarbonCopyUser
  },
  mounted() {
    users = document.querySelectorAll('.carbinCopies .user')
    if (users.length > 2) {
      this.hide()
    }
  },
  methods: {
    show() {
      for (var c of users) {
        c.style.display = 'flex'
      }
      this.$refs.show.style.display = 'none'
      this.$refs.hide.style.display = 'block'
    },
    hide() {
      for (var index in users) {
        index = parseInt(index, 10)
        if (index < 2) {
          continue
        }
        var c = users[index]
        if (!c) {
          break
        }
        c.style.display = 'none'
      }
      this.$refs.show.style.display = 'block'
      this.$refs.hide.style.display = 'none'
    }
  },
  props: {
    carbonCopies: Array
  }
}
</script>