package views

import (
	"os"
	"path/filepath"

	"github.com/alading/scm/front/kit/scripts/project2kit/helpers"
)

func formatFileName(path string) string {
	dir := filepath.Dir(path)
	fileName := filepath.Base(path)
	if fileName == "index.vue" {
		lastDir := filepath.Base(dir)
		path = filepath.Join(dir+"../", lastDir+".vue")
	}
	return path
}

func Rename(viewsPath string) {
	helpers.WalkFile(viewsPath, func(path string) error {
		ext := filepath.Ext(path)
		if ext != ".vue" {
			return nil
		}
		newName := formatFileName(path)
		if newName != path {
			err := os.Rename(path, newName)
			if err != nil {
				panic(err)
			}
		}
		return nil
	})

}
