<template v-if="text">
  <div style="display: inline-block; display: flex; align-items: center">
    <div
      :style="{
        height: `${size}px`,
        width: `${size}px`,
        borderRadius: '50%',
        background: rcolor,
        marginRight: '5px'
      }"
    ></div>
    <slot v-bind="{ color: tcolor }">
      <span :style="{ color: tcolor, text }">{{ text }} </span>
    </slot>
  </div>
</template>
<script>
const defaultColors = {
  blue: '#4d72fd',
  red: '#e04f52',
  green: '#71b620',
  yellow: '#fea90b',
  gray: '#cdcdcd'
}
export default {
  computed: {
    rcolor() {
      if (defaultColors[this.color]) {
        return defaultColors[this.color]
      }

      return this.color
    },
    tcolor() {
      if (this.sameColor) {
        return this.rcolor
      }

      return this.textColor
    }
  },
  props: {
    //圆点的大小
    size: {
      type: Number,
      default() {
        return 6
      }
    },
    color: String,
    textColor: String,
    //字体和点的颜色一致
    sameColor: Boolean,
    text: String
  }
}
</script>