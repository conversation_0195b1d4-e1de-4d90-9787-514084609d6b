<template>
  <el-dialog
    :show-close="true"
    v-on="$listeners"
    v-bind="$attrs"
    width="30%"
    title="提示"
    :close-on-click-modal="false"
  >
    <div>
      <h3 style="color: #24262a; font-weight: 600; font-size: 14px">
        <i
          style="
            color: #e59b00;
            font-size: 16px;
            margin-right: 6px;
            vertical-align: middle;
          "
          class="el-icon-warning"
        ></i>
        合同类型信息不完整，请补充后启用
      </h3>
      <p style="color: #777c94; font-size: 12px">
        发现【{{ hintObj.name }}】使用的以下信息不完整
      </p>
      <ul
        style="
          background-color: #f6fafd;
          padding: 20px 0 20px 40px;
          font-size: 12px;
        "
      >
        <li :key="index" v-for="(message, index) in hintObj.message">
          {{ message }}
        </li>
      </ul>
    </div>
    <div
      v-if="hadPrivilege('contract2.contractSet.typeManagement.manage')"
      :style="{
        textAlign: 'right',
        color: '#4f71ff'
      }"
    >
      <a
        :style="{
          cursor: 'pointer'
        }"
        @click="$router.push(`/types/${hintObj.id}/edit`)"
      >
        编辑合同类型
      </a>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="$emit('update:visible', false)">
        我知道了
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { hadPrivilege } from '../../../helpers/profile'
export default {
  props: {
    hintObj: {
      type: Object,
      default: () => {}
    }
  },
  methods: { hadPrivilege }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>