<template>
  <div class="pickers" style="width: 800px; margin: 0 auto">
    <h1>选择 部门，员工，角色，公司等</h1>
    <h2>多选部门</h2>
    <MultipleDepartment />
    <h2>单选部门</h2>
    <SingleDepartment />
    <h2>多选员工</h2>
    <MultipleEmployee />
    <h2>单选员工</h2>
    <SingleEmployee />
    <h2>部门下多选员工</h2>
    <MultipleEmployeeInsideDepartment />
    <h2>部门下单选员工</h2>
    <SingleEmployeeInsideDepartment />
    <h2>混合多选</h2>
    <Mixing />
  </div>
</template>

<script>
import MultipleDepartment from './multipleDepartment.vue'
import MultipleEmployee from './multipleEmployee.vue'
import SingleDepartment from './singleDepartment.vue'
import SingleEmployee from './singleEmployee.vue'
import MultipleEmployeeInsideDepartment from './multipleEmployeeInsideDepartment.vue'
import SingleEmployeeInsideDepartment from './singleEmployeeInsideDepartment.vue'
import Mixing from './mixing.vue'
export default {
  components: {
    MultipleDepartment,
    MultipleEmployee,
    SingleDepartment,
    SingleEmployee,
    MultipleEmployeeInsideDepartment,
    SingleEmployeeInsideDepartment,
    Mixing
  }
}
</script>

<style>
</style>