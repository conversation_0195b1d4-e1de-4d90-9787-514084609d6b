package pages

import (
	"fmt"
	"os"
	"regexp"
	"strings"
)

func formatHandleError(f, importLine string) (formattedImportLine string) {
	if !strings.Contains(importLine, "handleError") {
		return importLine
	}
	if strings.Contains(importLine, "handleSuccess") {
		return importLine
	}

	isHadMultiImportFn := false
	reg := regexp.MustCompile(`{\s+([A-z]+)\s+}`)
	matches := reg.FindStringSubmatch(importLine)
	if len(matches) > 2 {
		isHadMultiImportFn = true
	}

	//在同一行
	if strings.Contains(importLine, "import") && !isHadMultiImportFn {
		tmp := strings.Split(importLine, "from")

		p := strings.ReplaceAll(tmp[1], "/helpers", "/../helpers/handleError")

		return fmt.Sprintf("import handleError from %s", p)
	}

	//其他情况 转入错误处理 这里程序写起来麻烦
	fmt.Fprintln(os.<PERSON>derr, "need manually handle")
	fmt.Fprintln(os.Stderr, f)
	fmt.Fprintln(os.Stderr, importLine)

	return importLine
}
