<template>
  <div class="stepsWithDashLine">
    <div
      style="display: flex"
      :key="getKey(item)"
      v-for="(item, index) in steps"
    >
      <div
        :style="{
          flex: '0 0 24px',
          position: 'relative',
          top: top ? `${top}px` : '4px'
        }"
      >
        <div
          v-if="isNeedLine(index)"
          class="line"
          style="
            width: 1px;
            height: 100%;
            border-right: 1px dashed #ccc;
            position: absolute;
            left: 6px;
          "
        ></div>
        <slot name="icon" v-bind="{ item, index }">
          <div
            class="circle"
            style="
              width: 13px;
              height: 13px;
              border: 1px solid #ccc;
              border-radius: 50%;
              position: absolute;
              background-color: #fff;
              top: -2px;
            "
          ></div>
        </slot>
      </div>
      <div class="item" style="flex: 1 1 auto; margin-bottom: 16px">
        <slot v-bind="{ item, index }"></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  methods: {
    isNeedLine(index) {
      return index !== this.steps.length - 1
    },
    getKey(step) {
      if (step && step.key) {
        return step.key
      }
      //因为仅展示数据，不涉及到重建，所以这里使用随机也可以
      return Math.random()
    }
  },
  props: {
    steps: Array,
    //用于修正顶部距离 单位px
    top: {
      type: Number,
      default() {
        return 6
      }
    }
  }
}
</script>