<template>
  <div class="typesGroup" v-if="group">
    <div
      :style="{
        display: 'flex',
        marginBottom: '12px'
      }"
    >
      <Title
        :withPrefix="false"
        :title="`${group.name} (${group.approves.length})`"
      />
      <div
        :style="{
          display: 'flex'
        }"
      >
        <el-dropdown
          v-if="!sortOpen && group.name !== '其他'"
          :style="{
            marginRight: '10px'
          }"
          @command="
            cmd => {
              if (cmd === 'rename') {
                $emit('rename', group)
              }
              if (cmd === 'delete') {
                $emit('delete', group)
              }
            }
          "
          @visible-change="switchIcon"
        >
          <el-button>
            编辑分组
            <i ref="icon" class="el-icon-caret-bottom"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="rename">重命名</el-dropdown-item>
            <el-dropdown-item command="delete">删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button
          @click="sortOpen = true"
          v-if="!sortOpen && group.approves.length"
        >
          <i class="el-icon-sort" />排序
        </el-button>
        <el-button @click="confirmSort" v-if="sortOpen">确认</el-button>
        <el-button @click="cancelSort" v-if="sortOpen">取消</el-button>
      </div>
    </div>
    <DragableList
      v-model="approvals"
      :columns="columns"
      :showIndex="true"
      :allowDrag="sortOpen"
    >
      <template v-slot="{ item }">
        <div style="display: flex; flex-direction: column; overflow: hidden">
          <span
            :title="item.name"
            class="text-ellipsis-2line"
            style="
              font-weight: 500;
              font-size: 12px;
              color: #4f71ff;
              letter-spacing: 0;
              cursor: pointer;
            "
            type="text"
            @click="$emit('showApproval', item)"
            >{{ item.name }}</span
          >
          <!-- <el-tooltip class="item" effect="dark" :content="item.remark" placement="top"> -->
          <span
            :title="item.remark"
            class="text-ellipsis-2line"
            :style="{
              color: '#777C94'
            }"
          >
            {{ item.remark }}
          </span>
          <!-- </el-tooltip> -->
        </div>
        <div>
          <TextWidthDot
            :color="item.enable ? 'blue' : 'gray'"
            :text="item.enable ? '已启用' : '已停用'"
          />
        </div>
        <div>
          {{ item.updater.name }}
        </div>
        <div>
          {{ item.updateTime | formatDateTime('yyyy-MM-dd HH:mm') }}
        </div>
        <div
          :style="{
            display: 'flex'
          }"
        >
          <a
            v-if="!item.enable"
            :style="{
              marginRight: '20px',
              color: '#4F71FF',
              cursor: 'pointer'
            }"
            @click="$emit('editApproval', item)"
          >
            编辑
          </a>
          <a
            v-if="!item.enable"
            :style="{
              marginRight: '20px',
              color: '#4F71FF',
              cursor: 'pointer'
            }"
            @click="$emit('enableApproval', item)"
          >
            启用
          </a>

          <a
            v-if="item.enable"
            :style="{
              marginRight: '20px',
              color: '#4F71FF',
              cursor: 'pointer'
            }"
            @click="$emit('disableApproval', item)"
          >
            停用
          </a>

          <a
            v-if="!item.enable"
            :style="{
              color: '#4F71FF',
              cursor: 'pointer',
              marginRight: '20px'
            }"
            @click="$emit('deleteApproval', item)"
          >
            删除
          </a>
        </div>
      </template>
    </DragableList>
  </div>
</template>
<script>
import Title from '../title.vue'
import DragableList from '../draggableList.vue'
import TextWidthDot from '../textWithDot.vue'
export default {
  props: {
    group: {
      type: Object
    }
  },
  watch: {
    group: {
      handler(n) {
        this.group = n
        this.reload()
      }
    }
  },
  components: {
    Title,
    DragableList,
    TextWidthDot
  },
  created() {
    this.reload()
  },
  data() {
    return {
      columns: [
        {
          label: '流程名称',
          width: 'auto'
        },
        {
          label: '状态',
          width: '110px'
        },
        {
          label: '更新人',
          width: '120px'
        },
        {
          label: '更新时间',
          width: '160px'
        },
        {
          label: '操作',
          width: '150px'
        }
      ],
      sortOpen: false,
      approvals: []
    }
  },
  methods: {
    switchIcon(opened) {
      const iel = this.$refs.icon
      if (opened) {
        iel.className = 'el-icon-caret-top'
      } else {
        iel.className = 'el-icon-caret-bottom'
      }
    },
    reload() {
      this.approvals = this.group.approves
      this.approvals.sort((a, b) => (a.sort < b.sort ? -1 : 1))
    },
    cancelSort() {
      this.approvals = this.group.approves
      this.sortOpen = false
    },
    confirmSort() {
      var n = { ...this.group }
      for (var index in this.approvals) {
        this.approvals[index].sort = parseInt(index, 10)
      }
      n.approvals = this.approvals
      this.$emit('confirmSort', n.id, n.approvals)
      this.sortOpen = false
    }
  }
}
</script>