<template>
  <div>
    <span>
      {{ remark }}
    </span>
    <FieldContainer
      :key="field.name"
      v-for="field in filteredFields"
      :currentScale="$attrs.currentScale"
      :field="field"
      :type="'4'"
    >
      <Field :field="field" :width="'auto'" />
    </FieldContainer>
    <div v-if="!filteredFields.length && this.filter">暂无搜索内容</div>
    <div v-if="!filteredFields.length && !this.filter">暂无数据内容展示</div>
  </div>
</template>
<script>
import FieldContainer from './fieldContainer.vue'
import Field from './field.vue'
export default {
  components: {
    FieldContainer,
    Field
  },
  computed: {
    filteredFields() {
      if (!this.filter) {
        return this.fields
      }
      return this.fields.filter(item => item.name.includes(this.filter))
    }
  },
  props: {
    filter: String,
    remark: String,
    fields: {
      type: Array,
      validator(v) {
        for (var c of v) {
          if (!c.name) {
            return false
          }
        }
        return true
      }
    }
  }
}
</script>