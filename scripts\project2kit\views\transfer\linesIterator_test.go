package transfer

import (
	"strings"
	"testing"
)

func Test_linesIterator(t *testing.T) {
	result := linesIterator([]string{"a", "b", "c"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "b" {
			return []string{"a", "b", "c"}, currentIndex, currentIndex
		}

		return nil, -1, -1
	})

	if strings.Join(result, ",") != "a,a,b,c,c" {
		t.<PERSON><PERSON><PERSON>("linesIterator failed")
	}

	result = linesIterator([]string{"a", "b", "c", "d"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "c" {
			return []string{"t", "t", "t"}, 0, 3
		}

		return nil, -1, -1
	})

	if r := strings.Join(result, ","); r != "t,t,t" {
		t.<PERSON><PERSON><PERSON>("expected t,t,t but got %s", r)
	}

	result = linesIterator([]string{"a", "b", "c", "d"}, func(lines []string,
		currentIndex int) (
		newLines []string, start, end int) {
		if lines[currentIndex] == "c" {
			return []string{"t", "t", "t"}, 2, 2
		}

		return nil, -1, -1
	})

	if strings.Join(result, ",") != "a,b,t,t,t,d" {
		t.Errorf("expected a,b,t,t,t,d, but got %s", strings.Join(result, ","))
	}
}
