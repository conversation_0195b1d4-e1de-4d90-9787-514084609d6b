<template>
  <div
    :style="{
      height: '48px',
      overflow: 'hidden',
      borderBottom: '1px solid #EEF0F4',
      fontSize: '16px',
      paddingLeft: '20px',
      display: 'flex',
      alignItems: 'center'
    }"
  >
    <div>
      <a
        :style="{
          textDecoration: 'none',
          cursor: 'pointer',
          color: '#4f71ff'
        }"
        @click="$emit('back')"
      >
        <i class="el-icon-arrow-left" />返回
      </a>
      <span
        :style="{
          margin: '0 10px',
          color: '#ccc'
        }"
        >|</span
      >
      <span class="title">{{ title }}</span>
    </div>
    <!-- 中间区域 总是居中展示 -->
    <div
      :style="{
        fontSize: '14px',
        flex: '1 1 auto'
      }"
    >
      <div
        :style="{
          margin: '0 auto',
          width: '360px',
          fontSize: '14px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }"
      >
        <i
          class="el-icon-circle-check"
          :style="{
            color: '#A8ACBA',
            fontSize: '18px',
            marginRight: '2px'
          }"
          v-if="step === 1"
        />
        <div class="circle" v-if="step === 0">1</div>
        <div
          :style="{
            color: step === 0 ? '#4f71ff' : '#A8ACBA'
          }"
        >
          {{ steps && steps[0] ? steps && steps[0] : '设置模板信息' }}
        </div>
        <hr
          color="#CBCED8"
          :style="{
            width: '100px',
            borderTop: 'none'
          }"
        />
        <div
          class="circle"
          :style="{
            borderColor: step === 0 ? '#A8ACBA' : '#4f71ff',
            color: step === 0 ? '#A8ACBA' : '#4f71ff'
          }"
        >
          2
        </div>
        <div
          :style="{
            color: step === 0 ? '#A8ACBA' : '#4f71ff'
          }"
        >
          {{ steps && steps[1] ? steps && steps[1] : '设定签署和填写位置' }}
        </div>
      </div>
    </div>
    <!-- 操作区域 总是右侧 -->
    <div
      :style="{
        marginRight: '20px',
        display: 'flex'
      }"
    >
      <el-button v-if="step === 0" @click="() => $emit('cancel')">
        取消
      </el-button>
      <el-button v-if="step === 1" @click="() => $emit('prev')">
        上一步
      </el-button>
      <el-button type="primary" v-if="step === 0" @click="() => $emit('next')">
        下一步
      </el-button>
      <el-button
        v-if="step === 1"
        type="primary"
        @click="() => this.$emit('submit')"
      >
        {{ submitBtnText ? submitBtnText : '提交' }}
      </el-button>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: String,
    submitBtnText: String,
    steps: Array,
    step: {
      type: Number,
      default() {
        return 0
      }
    }
  }
}
</script>
<style scoped>
.circle {
  width: 14px;
  height: 12px;
  font-size: 12px;
  border-radius: 50%;
  border: 1px solid #4f71ff;
  color: #4f71ff;
  margin-right: 2px;
  padding-bottom: 2px;
  text-align: center;
}
</style>