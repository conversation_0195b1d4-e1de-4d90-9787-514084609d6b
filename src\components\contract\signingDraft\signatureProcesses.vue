<template>
  <div class="approvalProcesses">
    <b>填写</b>
    <StepsWithDashedLine :steps="writeableUsers">
      <template v-slot="{ item }">
        <WriterOrSigner :users="item" />
      </template>
    </StepsWithDashedLine>
    <b>签署</b>
    <StepsWithDashedLine :steps="signableUsers">
      <template v-slot="{ item }">
        <WriterOrSigner :users="item" />
      </template>
    </StepsWithDashedLine>
  </div>
</template>
<script>
import StepsWithDashedLine from '../stepsWithDashedLine.vue'
import WriterOrSigner from './user/writerOrSigner.vue'
export default {
  created() {
    console.log('writeableUsers', this.writeableUsers, this.signableUsers)
  },
  components: {
    StepsWithDashedLine,
    WriterOrSigner
  },
  props: {
    writeableUsers: Array,
    signableUsers: Array
  }
}
</script>