<template>
  <div class="attachmentFiles">
    <div
      class="tip"
      :style="{
        display: 'flex',
        background: '#F8F8F8',
        padding: '10px',
        borderRadius: '8px',
        marginBottom: '5px'
      }"
      v-if="!closed"
    >
      <div>
        附件仅供查阅不签署，不计入合同签署数量。如需签章，请添加至模板待签署文件。
      </div>
      <i
        class="el-icon-close"
        @click="close"
        :style="{
          cursor: 'pointer',
          marginLeft: '10x'
        }"
      />
    </div>
    <div
      :key="file.archiveId"
      v-for="file in files"
      :style="{
        padding: '2px 5px',
        background: '#F8F8F8',
        borderRadius: '8px',
        marginBottom: '10px',
        marginTop: '10px'
      }"
      :title="file.name"
      class="text-ellipsis-2line"
    >
      {{ file.name }}
    </div>
  </div>
</template>
<script>
import store from '../../../helpers/store'
export default {
  props: {
    files: Array
  },
  created() {
    this.closed = store.get('__attachmentFilesTipClosed')
  },
  methods: {
    close() {
      store.set('__attachmentFilesTipClosed', true)
      this.closed = true
    }
  },
  data() {
    return {
      closed: false
    }
  }
}
</script>