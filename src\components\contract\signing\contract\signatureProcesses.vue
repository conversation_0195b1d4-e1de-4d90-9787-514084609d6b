<template>
  <div class="approvalProcesses">
    <div
      v-if="writeProcesses && writeProcesses.length"
      style="font-size: 14px; font-weight: 400; color: #24262a"
    >
      填写
    </div>
    <StepsWithDashedLine :steps="writeProcesses">
      <template v-slot="{ item }">
        <Writer :writer="item" />
      </template>
    </StepsWithDashedLine>
    <div
      v-if="signProcesses && signProcesses.length"
      style="font-size: 14px; font-weight: 400; color: #24262a"
    >
      签署
    </div>
    <StepsWithDashedLine :steps="signProcesses">
      <template v-slot="{ item }">
        <Signer :signer="item" />
      </template>
    </StepsWithDashedLine>
  </div>
</template>
<script>
import StepsWithDashedLine from '../../stepsWithDashedLine.vue'
import Writer from './writer.vue'
import Signer from './signer.vue'
export default {
  created() {
    console.log('writeableUsers', this.writeableUsers, this.signableUsers)
  },
  components: {
    StepsWithDashedLine,
    Writer,
    Signer
  },
  props: {
    writeProcesses: Array,
    signProcesses: Array
  }
}
</script>