package transfer

import "testing"

func Test_findPrevMatchStringIndex(t *testing.T) {
	steps := findPrevMatchString([]string{
		"import {",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  getDictListByTypesAndCodes,",
	}, 4, "import {")

	if steps != 4 {
		t.<PERSON>rror("steps should be 4")
	}

	steps = findPrevMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  </div>,",
	}, 3, "<")

	if steps != 3 {
		t.Error("steps should be 3")
	}

	//-1 case
	steps = findPrevMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
	}, 2, "import")

	if steps != -1 {
		t.Error("steps should be -1")
	}

}

func Test_findNexMatchStringIndex(t *testing.T) {
	steps := findNexMatchString([]string{
		"import {",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  getDictListByTypesAndCodes,",
	}, 1, "getDictListByType")

	if steps != 1 {
		t.Errorf("steps should be 1, but got %d", steps)
	}

	steps = findNexMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  </div>,",
	}, 1, ">")

	if steps != 3 {
		t.Error("steps should be 3")
	}

	//-1 case
	steps = findNexMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
	}, 1, "import")

	if steps != -1 {
		t.Error("steps should be -1")
	}
}
