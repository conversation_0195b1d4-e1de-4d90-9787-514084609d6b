import { getToken } from '../../utils/token.js'

Page({
  data: {
    webviewSrc: ''
  },

  onLoad() {
    this.loadWebview()
  },

  onShow() {
    // 当页面显示时，重新加载webview以确保H5页面能重新执行认证检查
    if (this.data.webviewSrc) {
      this.loadWebview()
    }
  },

  loadWebview() {
    const token = getToken()

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    // 添加时间戳参数强制刷新webview
    const timestamp = Date.now()
    this.setData({
      // webviewSrc: `https://linggong.olading.com/task/mine?token=${token}&_t=${timestamp}`
      webviewSrc: `https://156-dev.olading.com/task/mine?token=${token}&_t=${timestamp}`
    })
  }
})

