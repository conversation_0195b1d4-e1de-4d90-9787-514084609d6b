<template>
  <div>
    <el-upload
      :headers="headerToken"
      ref="upload"
      class="upload"
      drag
      :data="uploadData"
      :multiple="true"
      :file-list="fileList"
      :onSuccess="onSuccess"
      :limit="limit"
      :on-remove="onRemove"
      :before-upload="beforeUpload"
      :accept="accept"
      :on-preview="handlePreview"
      :action="uploadUrl"
    >
      <el-tooltip
        class="item"
        :disabled="!isDisabled"
        :key="isDisabled+'n'"
        effect="dark"
        :content="'最多上传'+this.limit+'个文件'"
        placement="top-start"
      >
        <el-button
          :disabled="isDisabled"
          style="
            width: 88px;
            border: 1px solid #cad0dbff;
            color: #1e2228;
            display: flex;
            justify-content: center;
          "
          plain
          >{{ buttonText }}</el-button
        >
      </el-tooltip>
    </el-upload>
  </div>
</template>

<script>
import deepClone from 'kit/helpers/deepClone.js'
import { handleError } from 'kit/helpers/marketingBossToken.js'
import { getToken } from 'kit/helpers/token'

import fundTransfeMarketingClient from 'kit/services/fund-transfe/makeClient'
const fundTransfeClient = fundTransfeMarketingClient()

export default {
  props: {
    buttonText: {
      type: String,
      default: '选择文件'
    },
    accept: {
      type: String,
      default: ''
    },
    limit: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      uploadData: {},
      fileList: []
    }
  },
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/public/uploadFile`
    },
    isDisabled() {
      return this.fileList.length >= this.limit
    }
  },
  methods: {
    handlePreview(item) {
      const fileId = item.fileId || item?.response?.data?.fileId
      window.location.href = window.env.api + `/public/downloadFile/${fileId}`
    },
    validateField() {
      if (!this.elFormItem || !this.elForm) return
      this.elForm.validateField(this.elFormItem.prop)
    },
    onInput() {
      this.$emit('input', deepClone(this.fileList))
      this.$nextTick(()=>{
        this.$emit('rawFileList', this.getFileList())
      })
      this.validateField()
    },
    onSuccess(response, file, fileList) {
      if (response.success) {
        this.fileList = fileList
        this.onInput()
      } else {
        this.$message.error(response.message)
      }
    },
    onRemove(file) {
      this.fileList = this.fileList.filter(item => item.uid !== file.uid)
      this.onInput()
    },
    beforeUpload(file) {
      const isLimit = file.size / 1024 / 1024 < 20

      if (!isLimit) {
        this.$message.error('附件文件最大支持20M，请重新上传')
        return false
      }

      return true
    },
    async setFileList(fileList) {
      const newFileList = []
      for (let fileId of fileList) {
        const [err, result] = await fundTransfeClient.publicDescribeFile({
          body: {
            id: fileId
          }
        })
        if (err) return handleError(err)
        newFileList.push({
          fileId,
          name: result.data.name
        })
      }

      this.fileList = newFileList
      this.onInput()
    },
    getFileList() {
      const fileList = this.$refs.upload.fileList
      return fileList
    }
  }
}
</script>
<style scoped>
.upload ::v-deep.el-upload-dragger{
    width: auto;
}
.upload ::v-deep .el-upload-dragger {
  padding: 0;
  border: 0 !important;
  height: auto !important;
}
</style>