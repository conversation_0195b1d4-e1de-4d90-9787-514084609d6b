package components

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
)

func isOutParentDir(path, f, from string) bool {
	fullPath := fmt.Sprintf("%s/%s", filepath.Dir(f), from)
	dir := filepath.Dir(fullPath)

	if !strings.Contains(dir, path) {
		return true
	}

	return false
}

// handleError 由人工维护
func Format(path, f, line string) (formattedLine string) {
	reg := regexp.MustCompile(`from ['"](.*?)['"]`)
	matches := reg.FindStringSubmatch(line)
	if len(matches) < 2 {
		panic("no match found")
	}

	if !isOutParentDir(path, f, matches[1]) {
		return line
	}

	if strings.Contains(line, "'..") {
		formattedLine = strings.ReplaceAll(line, "'..", "'../..")
	}

	println(line)
	println(formattedLine)
	println()

	return formattedLine
}
