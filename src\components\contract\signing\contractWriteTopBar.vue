<template>
  <div
    :style="{
      height: '48px',
      overflow: 'hidden',
      borderBottom: '1px solid #EEF0F4',
      fontSize: '16px',
      paddingLeft: '20px',
      display: 'flex',
      alignItems: 'center'
    }"
  >
    <div>
      <a
        :style="{
          textDecoration: 'none',
          cursor: 'pointer',
          color: '#4f71ff'
        }"
        @click="$emit('back')"
      >
        <i class="el-icon-arrow-left" />返回
      </a>
      <span
        :style="{
          margin: '0 10px',
          color: '#ccc'
        }"
        >|</span
      >
      <span class="title" style="color: #24262a">{{ title }}</span>
    </div>
    <!-- 占中间位 -->
    <div style="flex: 1 1 auto"></div>
    <!-- 操作区域 总是右侧 -->
    <div
      :style="{
        marginRight: '20px',
        display: 'flex'
      }"
    >
      <el-button v-if="showUrge" plain @click="$emit('urge')"> 催办 </el-button>
      <el-button v-if="showWithdraw" plain @click="$emit('withdraw')">
        撤回
      </el-button>
      <el-button v-if="showAduit" plain @click="$emit('aduit')">
        审核
      </el-button>
      <el-button v-if="showReissue" plain @click="$emit('reissue')">
        重新发起
      </el-button>
      <el-button v-if="showTerminate" plain @click="$emit('terminate')">
        解约
      </el-button>
      <el-button v-if="showRenewal" plain @click="$emit('renewal')">
        续签
      </el-button>
      <el-button v-if="showModify" plain @click="$emit('modify')">
        变更
      </el-button>
      <el-button v-if="showDownload" plain @click="$emit('download')">
        下载
      </el-button>
      <el-button
        v-if="$listeners.submit"
        type="primary"
        @click="$emit('submit')"
      >
        {{ submitText }}
      </el-button>
    </div>
  </div>
</template>
<script>
import { user } from '../../../helpers/profile'
import {
  verifyAduit,
  verifyContractDonwload,
  verifyDownload,
  verifyModify,
  verifyReissue,
  verifyRenewal,
  verifyTerminate,
  verifyUrge,
  verifyWithdraw
} from '../../../pages/contract/contracts/verify'

export default {
  props: {
    submitText: {
      type: String,
      default() {
        return '提交填写'
      }
    },
    title: {
      type: String,
      default() {
        return '填写合同'
      }
    },
    detailInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      userId: 0
    }
  },
  async created() {
    const cuser = user
    this.userId = cuser.id
  },
  computed: {
    // 是否显示催办
    showUrge() {
      return verifyUrge(
        this.detailInfo.status,
        this.userId,
        this.detailInfo.creator?.signer?.id
      )
    },
    // 是否显示撤回
    showWithdraw() {
      return verifyWithdraw(
        this.detailInfo.status,
        this.userId,
        this.detailInfo.creator?.signer?.id
      )
    },
    // 是否显示审核
    showAduit() {
      // return true
      return verifyAduit(
        this.detailInfo.status,
        this.userId,
        this.detailInfo?.handlingBy?.id
      )
    },
    // 是否显示重新发起
    showReissue() {
      return verifyReissue(
        this.detailInfo.status,
        this.userId,
        this.detailInfo.creator?.signer?.id
      )
    },
    // 是否显示解约
    showTerminate() {
      return verifyTerminate(
        this.detailInfo.processStatus,
        this.detailInfo.terminateStatus,
        this.detailInfo.certifiedUser
      )
    },
    // 是否显示签约
    showRenewal() {
      // return true
      return verifyRenewal(
        this.detailInfo.processStatus,
        this.detailInfo.terminateStatus,
        this.detailInfo.renewalStatus
      )
    },
    // 是否显示变更
    showModify() {
      // return true
      return verifyModify(
        this.detailInfo.processStatus,
        this.detailInfo.terminateStatus,
        this.detailInfo.certifiedUser
      )
    },
    // 是否显示下载
    showDownload() {
      // return true
      return verifyContractDonwload(
        this.detailInfo.status,
        this.userId,
        this.detailInfo.creator?.signer?.id
      )
    }
  }
}
</script>
<style scoped>
.circle {
  width: 14px;
  height: 12px;
  font-size: 12px;
  border-radius: 50%;
  border: 1px solid #4f71ff;
  color: #4f71ff;
  margin-right: 2px;
  padding-bottom: 2px;
  text-align: center;
}
</style>