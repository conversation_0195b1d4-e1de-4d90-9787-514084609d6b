<template>
  <div class="signProcesses">
    <div class="write" v-if="needWriteSignSteps.length">
      <Title title="填写" style="margin-bottom: 10px" />
      <StepsWithDashedLine :steps="needWriteSignSteps">
        <template v-slot="{ item: signStep }">
          <div style="display: flex; justify-content: space-between">
            <TextWithDot :color="color(signStep)" :text="signStep.name" />
            <span v-if="writeCompleted(signStep)" style="color: #aaaaaa">
              已设置
            </span>
            <span v-else style="color: #4e71fc">请设置填写字段</span>
          </div>
        </template>
      </StepsWithDashedLine>
    </div>
    <div class="sign">
      <Title title="签署" style="margin-bottom: 10px" />
      <StepsWithDashedLine :steps="needSignSignSteps">
        <template v-slot:icon="{ index }">
          <div
            style="
              width: 9px;
              height: 9px;
              border-radius: 50%;
              border: 1px solid #ccc;
              text-align: center;
              position: relative;
              background-color: #fff;
            "
          >
            <span
              style="position: relative; top: -10px; font-size: 12px; zoom: 0.7"
              >{{ index + 1 }}</span
            >
          </div>
        </template>
        <template v-slot="{ item: signStep }">
          <div style="display: flex; justify-content: space-between">
            <TextWithDot :color="color(signStep)" :text="signStep.name" />
            <span v-if="signCompleted(signStep)" style="color: #aaaaaa">
              已设置
            </span>
            <span v-else style="color: #4e71fc">请设置签章</span>
          </div>
        </template>
      </StepsWithDashedLine>
    </div>
  </div>
</template>
<script>
import Title from '../title.vue'
import { getColorByString } from './colors'
import isSignatureFieldType from './isSignatureFieldType'
import StepsWithDashedLine from '../stepsWithDashedLine.vue'
import TextWithDot from '../textWithDot.vue'
export default {
  props: {
    fields: Array,
    pageFields: Array,
    signSteps: Array
  },
  components: {
    Title,
    StepsWithDashedLine,
    TextWithDot
  },
  computed: {
    needWriteSignSteps() {
      return this.signSteps.filter(item => item.needWrite)
    },
    needSignSignSteps() {
      return this.signSteps.filter(item => item.needSign)
    }
  },
  methods: {
    writeCompleted(signStep) {
      const fields = this.fields.filter(
        item =>
          item.signStepId === signStep.id && !isSignatureFieldType(item.type)
      )
      if (!fields.length) {
        return false
      }
      for (var c of fields) {
        const pageFields = this.pageFields.filter(item => item.fieldId === c.id)
        if (pageFields.length) {
          return true
        }
      }

      return false
    },
    signCompleted(signStep) {
      const fields = this.fields.filter(
        item =>
          item.signStepId === signStep.id && isSignatureFieldType(item.type)
      )
      if (!fields.length) {
        return false
      }
      for (var c of fields) {
        const pageFields = this.pageFields.filter(item => item.fieldId === c.id)
        //@todo 是否需要考虑签署日期
        if (pageFields.length) {
          return true
        }
      }

      return false
    },
    color(signStep) {
      return getColorByString(signStep.id + '')
    }
  }
}
</script>