<template>
  <div>
    <div class="item">
      <div class="title">合同履约状态</div>
      <ProcessStatusWithDot :value="contract.processStatus" />
      <span
        v-if="
          contract.renewalStatus === ContractRenewalStatusRenewing ||
          contract.renewalStatus === ContractRenewalStatusRenewed
        "
      >
        ({{ renewalStatus2string(contract.renewalStatus) }})
      </span>
    </div>

    <div class="item" v-if="contract.startTime">
      <div class="title">合同有效期</div>
      {{ contract.startTime && contract.startTime.split(' ')[0] }}

      {{
        contract.endTime
          ? `~ ${contract.endTime.split(' ')[0]}`
          : '~ 结束日期(无)'
      }}
    </div>

    <!-- 解约相关信息 -->
    <div class="item" v-if="contract.contractTermination">
      <div class="title">发起解约时间</div>
      {{ contract.contractTermination.createTime }}
    </div>

    <div
      class="item"
      v-if="contract.contractTermination && contract.contractTermination.reason"
    >
      <div class="title">解约原因</div>
      {{ contract.contractTermination.reason }}
    </div>

    <div
      class="item"
      v-if="contract.contractTermination && contract.contractTermination.remark"
    >
      <div class="title">备注</div>
      {{ contract.contractTermination.remark }}
    </div>

    <div
      class="item"
      v-if="contract.contractTermination && contract.contractTermination.type"
    >
      <div class="title">解约协议类型</div>
      {{ terminationType2string(contract.contractTermination.type) }}
    </div>
  </div>
</template>
<script>
import terminationType2string from '../../../services/contract/terminationType2string'
import ProcessStatusWithDot from './processStatusWithDot.vue'
import renewalStatus2string from '../../../services/contract/renewalStatus2string'
import {
  ContractRenewalStatusRenewing,
  ContractRenewalStatusRenewed,
  ContractStatusCompleted
} from '../../../services/contract/constants'
export default {
  components: {
    ProcessStatusWithDot
  },
  props: {
    contract: Object
  },
  data() {
    return {
      ContractRenewalStatusRenewing,
      ContractRenewalStatusRenewed,
      ContractStatusCompleted
    }
  },
  computed: {
    renewalStatus() {}
  },
  methods: {
    terminationType2string,
    renewalStatus2string
  }
}
</script>
<style>
.item {
  margin-bottom: 16px;
}
.title {
  color: #a8acba;
}
</style>