<template>
  <TextWithDot :color="color" :text="statusStr" />
</template>
<script>
import TextWithDot from '../textWithDot.vue'
import {
  TemplateStatusDraft,
  TemplateStatusOpened,
  TemplateStatusStopped
} from '../../../services/contract/constants'
import status2string from './status2string'
export default {
  computed: {
    statusStr() {
      return status2string(this.status)
    },
    color() {
      switch (this.status) {
        case TemplateStatusDraft:
          return 'yellow'
        case TemplateStatusOpened:
          return 'blue'
        case TemplateStatusStopped:
          return 'gray'
        default:
          throw new Error('not supported yet')
      }
    }
  },
  props: {
    status: String
  },
  components: {
    TextWithDot
  }
}
</script>