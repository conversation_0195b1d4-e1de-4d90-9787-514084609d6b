<template>
  <div class="pageFieldSettings" v-if="shown">
    <b>{{ subtitle }}</b>
    <div v-if="!pageField">请选择填充域或签章进行设置</div>
    <div v-if="pageField">
      <div class="setting" v-if="!isSignatureFieldType(field.type)">
        字段名称
        <span
          :style="{
            color: 'red'
          }"
        >
          *
        </span>
        <br />
        <el-input
          v-model="field.name"
          :disabled="field.isCustomControl ? false : true"
          @focus="
            () => {
              if (field.isCustomControl) {
                lastCustomField = { ...field }
              }
            }
          "
          @blur="e => changeCustomFieldName(e)"
        ></el-input>
      </div>

      <div class="setting">
        <span v-if="isSignatureFieldType(field.type)">选择签署方</span>
        <span v-if="!isSignatureFieldType(field.type)">选择参与方</span>
        <span
          :style="{
            color: 'red'
          }"
        >
          *
        </span>
        <br />
        <el-select :value="field.signStepId" @input="selectSignStep">
          <el-option
            :key="option.value"
            v-for="option in signatureOptions(field.type)"
            :label="option.label | label(option.type)"
            :value="option.value"
          ></el-option>
        </el-select>
      </div>

      <div
        class="setting"
        v-if="!isSignatureFieldType(field.type) && field.isCustomControl"
      >
        字段类型
        <span
          :style="{
            color: 'red'
          }"
        >
          *
        </span>
        <br />
        <el-select v-model="field.writeType" @change="selectWriteType">
          <el-option label="单行文本" value="1"></el-option>
          <el-option label="多行文本" value="2"></el-option>
          <el-option label="日期" value="3"></el-option>
        </el-select>
      </div>

      <div
        class="setting"
        v-if="field.writeType === WriteTypeDate || field.type === FieldTypeDate"
      >
        设置日期格式
        <br />
        <el-select v-model="pageField.dateFormat">
          <el-option label="yyyy年MM月dd日" value="yyyy年MM月dd日"></el-option>
          <el-option label="yyyy-MM-dd" value="yyyy-MM-dd"></el-option>
          <el-option label="yyyy/MM/dd" value="yyyy/MM/dd"></el-option>
          <el-option label="yyyy" value="yyyy"></el-option>
          <el-option label="MM" value="MM"></el-option>
          <el-option label="dd" value="dd"></el-option>
        </el-select>
      </div>

      <div
        :style="{
          display: 'flex'
        }"
        class="setting"
        v-if="!isSignatureFieldType(field.type)"
      >
        <div
          :style="{
            marginRight: '8px'
          }"
        >
          字体
          <br />
          <el-select v-model="pageField.font">
            <el-option label="宋体" value="宋体"></el-option>
            <el-option label="黑体" value="黑体"></el-option>
            <el-option label="楷体" value="楷体"></el-option>
          </el-select>
        </div>

        <div v-if="!isSignatureFieldType(field.type)">
          字号
          <br />
          <el-select :value="pageField.fontSize" @change="selectFontSize">
            <el-option label="12" :value="12"></el-option>
            <el-option label="14" :value="14"></el-option>
            <el-option label="16" :value="16"></el-option>
            <el-option label="18" :value="18"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="30" :value="30"></el-option>
            <el-option label="40" :value="40"></el-option>
          </el-select>
        </div>
      </div>

      <div class="setting" v-if="!isSignatureFieldType(field.type)">
        必填设置
        <span
          :style="{
            color: 'red'
          }"
        >
          *
        </span>
        <br />
        <el-radio-group v-model="field.writeRequired">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </div>

      <hr
        v-if="!isSignatureFieldType(field.type)"
        style="border: none; border-bottom: 1px solid #f2f2f2"
      />

      <div
        class="setting"
        v-if="!field.isCustomControl && !isSignatureFieldType(field.type)"
      >
        系统字段是否允许修改
        <i
          v-if="!systemConfigTipShown"
          title="系统字段发起合同签署时，自动从系统中获取值，默认不能修改内容。若允许修改，修改值将同步更新系统字段信息。"
          class="olading-iconfont oi-wenhao"
          style="color: #7f7f7f"
        />
        <div
          v-if="systemConfigTipShown"
          :style="{
            height: '74px',
            borderRadius: '8px',
            background: '#F8F8F8',
            padding: '10px',
            display: 'flex'
          }"
        >
          <span
            :style="{
              color: '#777C94',
              fontSize: '12px'
            }"
          >
            系统字段发起合同签署时，自动从系统中获取值，默认不能修改内容。若允许修改，修改值将同步更新系统字段信息。
          </span>
          <i
            class="el-icon-close"
            :style="{
              cursor: 'pointer',
              marginLeft: '10x'
            }"
            @click="closeSystemConfigTip"
          />
        </div>
        <el-checkbox v-model="field.modifiable" label="发起方可以修改" />
        <el-checkbox v-model="field.writeable" label="填写方可以修改" />
      </div>

      <div class="setting" v-if="field.isCustomControl">
        <el-checkbox
          v-model="field.common"
          @change="configCustomField"
          label="设为常用自定义字段"
        />
        <br />
        <span
          :style="{
            color: '#ccc'
          }"
        >
          设置后，其他模板中可使用此字段
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import { PageFieldDefaultWidth } from '../../../pages/contract/constants'
import {
  FieldTypeDate,
  WriteTypeDate,
  FieldTypeCompany,
  FieldTypePerson
} from '../../../services/contract/constants'
import isSignatureFieldType from './isSignatureFieldType'
import store from '../../../helpers/store'
export default {
  filters: {
    label(label, type) {
      if (type === FieldTypeCompany && !label.includes('企业')) {
        return `${label} (企业)`
      }
      if (type === FieldTypePerson && !label.includes('个人')) {
        return `${label} (个人)`
      }

      return label
    }
  },
  created() {
    const shown = store.get('__systemConfigTipShown')
    this.systemConfigTipShown = shown !== false
  },
  watch: {
    pageField: {
      handler(v) {
        if (
          this.field.writeType === WriteTypeDate &&
          !this.pageField.dateFormat
        ) {
          this.pageField.dateFormat = 'yyyy年MM月dd日'
        }
      },
      deep: true
    }
  },
  props: {
    field: Object,
    pageField: Object,
    signatures: Array
  },
  computed: {
    subtitle() {
      if (!this.field || !this.field.type) {
        return ''
      }
      if (isSignatureFieldType(this.field.type)) {
        if (this.field.name === '个人签名') {
          return '个人签章设置'
        }
        return `${this.field.name}设置`
      }
    },
    shown() {
      if (!this.field || !this.pageField || !this.signatures) {
        return false
      }
      if (!this.signatures.length) {
        return false
      }

      return true
    }
  },
  methods: {
    isSignatureFieldType,
    changeCustomFieldName(e) {
      if (!this.lastCustomField) {
        return
      }

      this.$emit('changeCustomFieldName', e.target.value, this.lastCustomField)

      this.lastCustomField = null
    },
    selectWriteType(v) {
      if (v === WriteTypeDate && !this.pageField.dateFormat) {
        this.pageField.dateFormat = 'yyyy年MM月dd日'
      }
    },
    configCustomField() {
      if (this.field.common) {
        this.$emit('saveCustomField', this.field)
        return
      }
      this.$emit('removeCustomField', this.field)
    },
    signatureOptions(type) {
      var options = []
      for (var c of this.signatures) {
        options.push({
          label: c.name,
          value: parseInt(c.id, 10),
          type: c.signerType
        })
      }
      var filterType = ''

      if (
        this.field.type === FieldTypeCompany ||
        this.field.type === FieldTypePerson
      ) {
        filterType = this.field.type
      }
      if (this.field.fieldGroupName === '合同公司信息') {
        filterType = FieldTypeCompany
      }
      if (this.field.fieldGroupName === '个人信息') {
        filterType = FieldTypePerson
      }
      if (filterType) {
        return options.filter(item => item.type === filterType)
      }

      return options
    },
    closeSystemConfigTip() {
      store.set('__systemConfigTipShown', false)
      this.systemConfigTipShown = false
    },
    selectFontSize(v) {
      this.pageField.fontSize = v
      this.pageField.height = v + 4

      const fw = this.field.name.length * v
      this.pageField.width =
        PageFieldDefaultWidth > fw ? PageFieldDefaultWidth : fw
    },
    selectSignStep(v) {
      var signStep = this.signatures.find(item => parseInt(item.id, 10) === v)
      if (!signStep) {
        throw new Error('logic error, signStep not existed')
      }
      this.$emit('selectSignStep', this.field, this.pageField, signStep)
    },
    changeFieldName(v) {
      console.log('changeFieldName', v)
      // this.$emit('changeFieldName', v)
    }
  },
  data() {
    return {
      WriteTypeDate,
      FieldTypeDate,
      systemConfigTipShown: true,
      lastCustomField: null
    }
  }
}
</script>
<style scoped>
.pageFieldSettings {
  font-size: 14px;
}
.setting {
  margin: 0 0 10px 0;
}
</style>