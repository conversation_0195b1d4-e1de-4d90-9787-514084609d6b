<template>
  <el-dialog
    :close-on-click-modal="false"
    title="批量签署提示"
    :visible.sync="dialogVisible"
    width="560px"
  >
    <div style="padding: 0 24px 0 48px">
      <h4
        style="color: rgba(36, 38, 42, 1); position: relative; font-size: 14px"
      >
        <i
          style="
            color: #e59b00;
            font-size: 16px;
            margin-right: 6px;
            vertical-align: middle;
            position: absolute;
            top: 2px;
            left: -22px;
          "
          class="el-icon-warning"
        ></i>
        您有 {{ accordTotal }} 份文件可签署，确认要批量签署吗？
      </h4>
      <p
        v-if="!isAllAccord"
        style="font-size: 14px; color: #777c94; margin: 7px 0 30px 0"
      >
        您选择的文件有
        {{ failTotal }}
        份无法签署，请检查您是否是当前签署人，并且文件为签署中状态。
      </p>
      <template v-if="isBatchSign">
        <p style="margin: 0 0 6px 0">温馨提示：</p>
        <div>
          <p class="small-font">1.请确保您已阅读并知晓所选签署任务的全部内容</p>
          <p class="small-font">
            2.点击签署后，系统会在文档的指定位置预设选中签章
          </p>
          <p class="small-font">
            3.一次意愿验证将完成所选批量签署任务的全部签署，请谨慎操作
          </p>
        </div>
      </template>
    </div>

    <span slot="footer" class="dialog-footer">
      <div>
        <el-checkbox v-if="isBatchSign" v-model="isKnown"
          ><span class="small-font"
            >我已阅读并知晓所选文档的内容</span
          ></el-checkbox
        >
      </div>
      <div>
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
import handleError from '../../../helpers/handleError'

export default {
  name: 'BatchSign',
  data() {
    return {
      isKnown: false,
      dialogVisible: false
    }
  },
  props: {
    rows: {
      type: Array,
      default: () => []
    },
    currentId: Number,
    // 可以签署的合同列表
    signAccordList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
    },
    async handleSubmit() {
      // 跳转到批量审核页面
      // 走批量签署
      if (this.isBatchSign) {
        if (!this.isKnown) {
          handleError({ message: '请勾选我已阅读并知晓内容' })
          return
        }
        const accordSignIdList = this.accordSignings.map(
          accordSign => accordSign.id
        )
        this.$router.push({
          path: '/signings/confirm',
          query: { accordSignIdList: JSON.stringify(accordSignIdList) }
        })
        this.close()
        return
      }
      // 走单个文件签署处理
      if (this.accordTotal === 1) {
        // 单文件签署逻辑
        this.$router.push(`contracts/${this.accordSignings[0].id}/sign`)
        this.close()
      }
    }
  },
  computed: {
    // 通过校验的合同
    accordSignings() {
      return this.signAccordList
    },
    // 通过校验的数量
    accordTotal() {
      return this.accordSignings.length
    },
    // 不满足签署条件的数量
    failTotal() {
      return this.rows.length - this.accordTotal
    },
    // 是否走批量审批
    isBatchSign() {
      return this.accordTotal > 1
    },
    // 是否全部符合条件
    isAllAccord() {
      return this.rows.length === this.accordTotal
    }
  }
}
</script>

<style scoped>
.small-font {
  color: #777c94;
  font-size: 12px;
  line-height: 16px;
  margin: 0;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .el-dialog__body {
  padding: 0;
}
</style>