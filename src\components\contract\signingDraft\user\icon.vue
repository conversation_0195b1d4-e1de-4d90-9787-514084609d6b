<template>
  <div class="userIcon" style="position: relative; top: -2px">
    <img
      v-if="type === SingerTypePerson"
      style="
        height: 16px;
        width: 16px;
        margin-right: 10px;
        position: relative;
        top: 2px;
        flex: 0 0 16px;
      "
      src="../../../../assets/images/icon/<EMAIL>"
    />
    <img
      v-if="type === SingerTypeCompany"
      style="
        height: 16px;
        width: 16px;
        margin-right: 10px;
        position: relative;
        top: 3px;
        flex: 0 0 16px;
      "
      src="../../../../assets/images/icon/<EMAIL>"
    />
  </div>
</template>
<script>
import {
  <PERSON><PERSON>ype<PERSON>erson,
  SingerTypeCompany
} from '../../../../services/contract/constants'
export default {
  props: {
    type: String
  },
  data() {
    return {
      <PERSON>Type<PERSON>erson,
      SingerTypeCompany
    }
  }
}
</script>