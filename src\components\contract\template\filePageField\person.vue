<template>
  <div
    :style="{
      height: `${pageField ? pageField.height : 158}px`,
      width: `${pageField ? pageField.width : 158}px`,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }"
  >
    <div
      :style="{
        textAlign: 'center'
      }"
      v-if="!value"
    >
      <img :src="icon" height="24" width="24" />
      <br />
      个人签名
    </div>
    <img
      v-if="value"
      :src="value"
      :style="{
        objectFit: 'scale-down',
        height: `${pageField ? pageField.height : 158}px`,
        width: `${pageField ? pageField.width : 158}px`
      }"
    />
  </div>
</template>
<script>
import icon from '../../../../assets/images/icon/<EMAIL>'
export default {
  props: {
    pageField: Object,
    value: ''
  },
  data() {
    return {
      icon
    }
  }
}
</script>