<template>
  <div
    class="boxWithTitle"
    :style="{
      borderRadius: '8px',
      padding: '20px 24px',
      background: '#fff'
    }"
  >
    <div
      :style="{
        display: 'flex'
      }"
      v-if="title"
    >
      <h2
        :style="{
          height: '16px',
          fontWeight: '600',
          fontSize: '16px',
          color: '#24262A',
          lineHeight: ' 16px',
          padding: 0,
          margin: 0,
          marginBottom: '12px',
          flex: '1 1 auto'
        }"
      >
        <span v-html="title"></span>
      </h2>
      <a
        :style="{
          flex: '0 0 28px',
          fontSize: '14px',
          color: '#4F71FF',
          marginRight: '10px',
          cursor: 'pointer'
        }"
        v-if="$listeners.more && !hideMore"
        @click="$emit('more')"
      >
        更多
      </a>
    </div>

    <slot></slot>
  </div>
</template>
<script>
export default {
  created() {
    console.log('lsten', this.$listeners)
  },
  props: {
    title: String,
    hideMore: Boolean
  }
}
</script>
<style scoped>
.boxWithTitle {
  transition: all 0.4s;
}
.boxWithTitle:hover {
  transform: translateY(-2px);
}
</style>