<template>
  <div class="top-progress-bar" v-if="steps.length">
    <template v-for="(step, index) in steps">
      <div
        class="step"
        :key="index"
        :class="{ active: step.value === value, ok: index < activeIndex }"
      >
        <div class="icon-step">
          <span
            v-if="index < activeIndex"
            class="icon iconfont icon-remind-complete"
          ></span>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <span> {{ step.label }}</span>
      </div>
      <i
        class="line"
        :class="{ active: index < activeIndex }"
        :key="index + 'line'"
        v-if="index !== steps.length - 1"
      />
    </template>
  </div>
</template>

<script>
import { validateLabelValueArray } from 'kit/helpers/propsValidator'
export default {
  computed: {
    activeIndex() {
      return this.steps.findIndex(
        step => String(step.value) === String(this.value)
      )
    }
  },
  props: {
    steps: {
      type: Array,
      required: true,
      validator: validateLabelValueArray,
      default: () => []
    },
    value: {
      type: [String, Number],
      required: true
    }
  }
}
</script>

<style scoped>
.top-progress-bar {
  display: flex;
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC';
  text-align: left;
  width: 800px;
  line-height: 24px;
  justify-content: space-between;
  position: relative;
  align-items: center;
}

.top-progress-bar .line {
  height: 1px;
  display: block;
  flex: 1;
  background: #e9e9e9;
}
.top-progress-bar .line.active {
  background: var(--o-primary-color);
}

.icon-step {
  width: 32px;
  height: 32px;
  border-radius: 32px;
  opacity: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #e4e7edff;
  margin-right: 8px;
  font-size: 14px;
  color: #828b9b;
}

.step {
  display: flex;
  align-items: center;
  background: #fff;
  z-index: 1;
  position: relative;
  padding: 0 16px;
}

.step.ok .icon-step {
  background: rgba(88,120,255, .1);
  color: #f77234;
}

.step.active .icon-step {
  background: #f77234;
  color: #fff;
}
</style>
