<template>
  <el-upload
    width="100%"
    :headers="headerToken"
    ref="upload"
    class="upload"
    drag
    :data="uploadData"
    :file-list="fileList"
    :on-remove="onRemove"
    :accept="accept"
    :action="uploadUrl"
  >
    <!-- <div
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30px 0 24px;
      "
    > -->
      <!-- <img src="kit/assets/images/upload-excel.svg" alt="" /> -->
      <el-button
        style="
          width: 88px;
          border: 1px solid #cad0dbff;
          color: #1e2228;
          display: flex;
          justify-content: center;
        "
        plain
        >{{ buttonText }}</el-button
      >
      <!-- <div style="color: #828b9b; font-size: 14px">
        支持xlsx和xls文件，文件大小不超过5M
      </div> -->
    <!-- </div> -->
  </el-upload>
</template>

<script>
import { getToken } from 'kit/helpers/token'
import fundTransfeMarketingClient from 'kit/services/fund-transfe/makeClient'
import handleError from 'kit/helpers/handleError'
const marketingClient = fundTransfeMarketingClient()

export default {
    props:{
        buttonText:{
            type:String,
            default:"选择文件"
        },
        accept:{
            type:String,
            default:""
        }
    },
  data() {
    return {
      headerToken: {
        Authorization: `${getToken()}`
      },
      uploadData: {},
      fileList: []
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env.api}/public/uploadFile`
    }
  },
  methods: {
    updateFileList(file, fileList) {
      for (var c of fileList) {
        if (c.uid === file.uid) {
          fileList.splice(fileList.indexOf(c), 1)
        }
      }
    },
    onChange(file, fileList) {
      let suffix = this.getFileType(file.name) // 获取文件后缀名
      let suffixArray = ['xls', 'xlsx'] // 限制的文件类型
      if (suffixArray.indexOf(suffix) === -1) {
        this.$message.error('文件格式错误，请重新选择！')
        this.updateFileList(file, fileList)
        return
      }
      const isLimit = file.size / 1024 / 1024 < 5
      if (!isLimit) {
        this.$message.error('导入文件的大小最多支持5M')
        this.updateFileList(file, fileList)
        return
      }
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      } else {
        this.fileList = []
      }
    },
    getFileType(name) {
      let startIndex = name.lastIndexOf('.')
      if (startIndex !== -1) {
        return name.slice(startIndex + 1).toLowerCase()
      } else {
        return ''
      }
    },
    onRemove() {
      this.fileList = []
    }
  }
}
</script>
<style scoped>
.upload{
    display: flex;
    flex-direction: column;
}
.upload ::v-deep .el-upload-dragger{
    padding: 0;
    border:0 !important;
    height: auto !important;
}
</style>