<template>
  <div
    :style="{
      position: 'fixed',
      bottom: '0',
      textAlign: 'center',
      height: '72px',
      lineHeight: '72px',
      background: '#fff',
      zIndex: 999,
      width: 'inherit',
      boxShadow: '0 -4px 8px 0 rgba(203,206,216,0.16)'
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
export default {
  mounted() {
    this.$el.style.width = `${this.$el.parentElement.clientWidth}px`
  }
}
</script>