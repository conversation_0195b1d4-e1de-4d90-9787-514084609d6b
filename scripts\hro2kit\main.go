package main

import (
	"flag"
	"os"
	"path/filepath"

	"github.com/alading/scm/front/kit/scripts/hro2kit/transfer"
)

var path string

func walkFile(path string, fn func(f string) error) {
	err := filepath.Walk(path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		return fn(path)
	})
	if err != nil {
		panic(err)
	}
}

func init() {
	flag.StringVar(&path, "path", "", "the need handle path")
}
func main() {
	flag.Parse()
	if path == "" {
		flag.Usage()
		return
	}

	walkFile(path, func(f string) error {
		// println("formatting file: " + f)
		transfer.Trans(f)
		return nil
	})
}
