<template>
  <div>
    <div
      v-for="template in templates"
      :key="template.id"
      style="display: flex; align-items: center; margin-bottom: 12px"
    >
      <el-radio
        v-model="templateId"
        :label="template.id"
        @input="id => templateIdChange(template, id)"
        style="
          margin-right: 0;
          width: 60px;
          display: flex;
          flex-direction: column;
          align-items: center;
        "
      >
      </el-radio>
      <div
        style="
          min-height: 66px;
          box-sizing: border-box;
          padding: 16px 20px;
          flex: 1;
          border: 1px solid #eef0f4;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        "
      >
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <h3
            style="
              margin: 0;
              font-weight: 400;
              font-size: 14px;
              color: #24262a;
              letter-spacing: 0;
              line-height: 14px;
              max-width: 500px;
            "
          >
            {{ template.name }}
          </h3>
          <span
            style="
              font-size: 12px;
              color: #a8acba;
              line-height: 12px;
              text-aligin: right;
            "
            >更新时间：{{ template.updateTime }}</span
          >
        </div>
        <p
          style="
            width: 400px;
            font-size: 12px;
            color: #777c94;
            line-height: 18px;
            margin: 0;
            max-width: 500px;
          "
        >
          {{ template.remark }}
        </p>
      </div>
    </div>
    <el-pagination
      :page-size.sync="pageSize"
      :pager-count="pagerCount"
      layout="prev, pager, next"
      :current-page.sync="currentPage"
      :total="templateTotal"
      @size-change="pageChange"
      @current-change="pageChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'tenmlateList',

  data() {
    return {
      templateId: '',
      pageSize: 5,
      currentPage: 1,
      pagerCount: 11,
      total: 0
    }
  },
  props: {
    templates: {
      type: Array,
      default: () => []
    },
    templateTotal: {
      type: Number
    }
  },
  methods: {
    templateIdChange(template, id) {
      this.$emit('input', template, id)
    },
    pageChange() {
      this.$emit('pageChange', this.currentPage, this.pageSize)
    }
  }
}
</script>

<style scoped>
::v-deep .el-radio__inner {
  width: 20px;
  height: 20px;
}
::v-deep .el-radio {
  overflow: hidden;
}
::v-deep .el-radio__label {
  position: absolute !important;
  left: 9999px !important;
}
</style>