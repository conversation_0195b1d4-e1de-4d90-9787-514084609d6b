<template>
  <div style="display: inline-block; padding-left: 2em; padding-left: 8px">
    <span v-if="value == 1" class="enableWithDot waite"> 尚未签署</span>
    <span v-else-if="value == 2" class="enableWithDot waite"> 未生效</span>
    <span v-else-if="value == 3" class="enableWithDot inProcess">生效中</span>
    <span v-else-if="value == 4" class="enableWithDot wasDue">到期终止</span>
    <span v-else-if="value == 5" class="enableWithDot termination"
      >提前终止</span
    >
    {{ status && `(${status})` }}
  </div>
</template>
<script>
import {
  ContractStatusRenewaling,
  ContractStatusRenewaled,
  ContractStatusTerminated,
  ContractStatusTerminateing,
  ContractStatusChangeing,
  ContractStatusChangeed
} from '../../../services/contract/constants'
// 合同履约状态：1-尚未签署；2-未生效(未到开始时间)；3-生效中(未到结束时间)；4-已到期；5-提前终止

export default {
  name: 'StatusWithDot',
  props: {
    value: {
      type: String,
      default() {
        return '1'
      }
    },
    renewalStatus: {
      type: String,
      default: '1'
    },
    terminateStatus: {
      type: String,
      default: '1'
    },
    changeStatus: {
      type: String,
      default: '1'
    }
  },
  computed: {
    status() {
      switch (this.terminateStatus) {
        case ContractStatusTerminateing:
          return '解约中'
        case ContractStatusTerminated:
          return '已解约'
      }
      switch (this.renewalStatus) {
        case ContractStatusRenewaling:
          return '续约中'
        case ContractStatusRenewaled:
          return '已续约'
      }
      // switch (this.changeStatus) {
      //   case ContractStatusChangeing:
      //     return '变更中'
      //   case ContractStatusChangeed:
      //     return '已变更'
      // }
      return ''
    }
  }
}
</script>

<style  scoped>
/* 红#F63939 灰#CCCCCC 蓝#4F71FF 绿#07bb06*/
.enableWithDot::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: relative;
  left: -6px;
}
/* 已到期 */
.enableWithDot.wasDue::before {
  background-color: #cccccc;
}
/* 未生效 */
.enableWithDot.waite::before {
  background-color: #4f71ff;
}
/* 已生效 */
.enableWithDot.inProcess::before {
  background-color: #70b603;
}
/* 提前终止   */
.enableWithDot.termination::before {
  background-color: #f63939;
}
</style>