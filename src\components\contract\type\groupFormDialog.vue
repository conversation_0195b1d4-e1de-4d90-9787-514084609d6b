<template>
  <el-dialog
    v-bind="$attrs"
    v-on="$listeners"
    :title="`${group ? '重命名' : '新建分组'}`"
    @close="closeDialog"
    @open="openDialog"
    :close-on-click-modal="false"
  >
    <el-form
      ref="groupForm"
      label-position="top"
      :model="groupForm"
      :rules="rules"
    >
      <el-form-item prop="name" label="分组名称">
        <el-input
          maxlength="20"
          v-model="groupForm.name"
          placeholder="请输入分组名称"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible')">取消</el-button>
      <el-button type="primary" @click="submit"> 提交 </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    group: {
      type: Object
    },
    existedGroups: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      groupForm: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称' },
          {
            validator: this.validGroupFormName
          }
        ]
      }
    }
  },
  methods: {
    openDialog() {
      if (!this.group) {
        return
      }
      this.groupForm.name = this.group.groupName
    },
    closeDialog() {
      this.$refs.groupForm.resetFields()
    },
    submit() {
      this.$refs.groupForm.validate(valid => {
        if (!valid) {
          this.scrollIntoError(this.$refs.groupForm)
          return
        }
        this.$emit('submit', this.groupForm)
        this.$refs.groupForm.resetFields()
      })
    },
    validGroupFormName(rule, value, cb) {
      for (var c of this.existedGroups) {
        if (this.group && this.group.groupId === c.groupId) {
          continue
        }

        if (c.groupName === value.trim()) {
          cb(new Error('分组名称不能重复'))
          return
        }
      }
      cb()
    }
  }
}
</script>