# 将 hro 项目迁移到 kit

算法描述 将 vue 文件按行转为数组

从中解析出代码块

替换掉相关代码块

其中有些代码块是有关联的 如 import components template 下使用

## public 中图片 to assets/hro

图片移动

vue 中相关位置的替换

## api 层 to services/hro

vue 中相关使用

去掉 axios，改为 httpClient

## view 层 to pages/hro

人工迁移
view 下面的 components 实际是页面片

## components 层 to components/hro

人工处理

视图组件中 不应该存在 api 调用

## util 层 to utils/hro

人工处理

## filters 有点多 看看是不是可以做成函数 不挂载在全局

因为直接将 filters 挂载 vue，一些同学不知道，又重复，不如约定，不挂载全局 filter

## 杂项

【已完成】sass 去除 脚本转义

tailwindcss 去除

lodash 人工去除 编辑器替换

dayjs 人工去除

轮播图 不依赖外部 js 库 人工去除

## 思考

为什么要引入 vant 对于 PC 来说 不应该

## 执行

【已完成】api 层人工转为 httpClient 方式
利用 vscode 搜索功能，正则搜索 post("xxx")得到基础列表
利用 gclient 生成 hro api

platform 同理，另以下 API 调用机制存在特殊形式

getPlatformLegalApi:platformListLegal

loginApi:platformLogin

createCaptchaApi:platformCreateCaptcha

createOtpApi:platformCreateOtp

uploadFileApi:platformUploadFile

---

todo getCaptchaURL 需要改为直接使用

todo uploadFileApi parameters.temp = true;

todo getPreviewFileUrl

todo getMerchantApi isReturnNativeResponse:true,ifShowErrorModal:false

todo temCompatibleFormatter 考虑先放到 responseInterceptor 下 正规应该 view 层调用

todo getCustomerCorporationPageApi
{
"filters": {},
"limit": 1000,
"start": 0,
"withDeleted": true,
"withDisabled": true,
"withTotal": true
})
return formatter2SelectOptions(list, "name", "id")

todo getAccountantDetailApi
temCompatibleFormatter(data)
temCompatibleFormatter(data.basicInfo)
temCompatibleFormatter(data.billInfo.contractItemList)
temCompatibleFormatter(data.financeInfo)

todo options.js 文件较特殊 双层 format 且更改了 api options 参数

```
api 函数列表
'approvalProcessListApi',

'getBillListApi',
'addBillListApi',
'billConfirmApi',
'_api_bill_item_update_',
'billOffsetApi',
'billRollBackApi',
'_api_bill_sendEmail_',
'billSubmitApi',
'getBillDetailApi',
'getBillItemListApi',
'getBillItemDetailApi',
'getEmployeeOtherFeeListApi',
'itemExportApi',


'getCandidateDetailApi',
'abandonCandidateApi',
'waitEntryCandidateApi',


'getMerchantApi',
'getIncrementInfoApi',


'addCustomerApi',
'getCustomerBasicDetailApi',
'addCustomerContractApi',
'updateCustomerContractApi',
'getCustomerContractDetailApi',
'getCustomerContractListApi',
'updateCustomerContractManagerApi',
'customerContractStopApi',
'getCustomerCorporationListApi',
'getCustomerListApi',
'updateCustomerStatusApi',
'updateCustomerApi',
'checkNameApi',
'checkCodeApi',
'corporationCheckNameApi',
'contractCheckNameApi',
'contractRenewApi',
'getAccountCustomerContractListApi',
'getAccountCustomerContractItemListApi',


'addInvoiceApi',
'invoiceConfirmApi',
'invoiceDetailApi',
'invoiceInvalidApi',
'invoiceListApi',
'invoiceMailApi',
'invoiceSendApi',
'invoiceUpdateApplyApi',
'invoiceMakeApi',
'invoiceBackApi',
'invoiceReceivedApi',


'getCustomerCorporationPageApi',
'getByIdCustomerCorporationListApi',
'getCustomerContractListOptionsApi',
'getSupplierCorporationPageApi',
'getByIdSupplierCorporationListApi',
'getCustomerOptionsApi',
'getPlatformLegalOptionsApi',
'getAccountsetListApi',
'getSupplierListOptionsApi',
'getBusinessOptionsApi',
'getServiceContentApi',
'getInvoiceContentListApi',


'getPaymentListApi',
'billListApi',
'detailApi',
'exportTemplateApi',
'importPaymentApi',
'offsetApi',


'getPlatformLegalApi',
'loginApi',
'createCaptchaApi',
'modifyPasswordApi',
'createOtpApi',
'getCaptchaURL',
'getProfileApi',
'addCalendarManagementApi',
'getOneDayListApi',
'getCalendarManagementListApi',
'updateStatusCalendarManagementApi',
'updateCalendarManagementApi',
'renewTokenApi',
'uploadFileApi',
'getPreviewFileUrl',
'listFileApi',


'getAccountantListApi',
'updateAccountantApi',
'enableAccountantApi',
'disableAccountantApi',
'addAccountantApi',
'getAccountantDetailApi',
'accountsetCheckNameApi',
'addInvoiceContentApi',
'getAccountsetItemListApi',


'getSupplierListApi',
'addSupplierApi',
'getSupplierBasicDetailApi',
'updateSupplierApi',
'addSupplierContractApi',
'getSupplierCorporationListApi',
'stopSupplierContractApi',
'disableSupplierApi',
'enableSupplierApi',
'updateContractManagerApi',
'contractRenewApi',
'checkCodeApi',
'checkNameApi',


'listRequestParams',
```
