//P 为修正量，修正与后台pdf生成时候 行高不一致问题
//这是 一个经验量，由多次调试获取
import { TextareaLineHeight } from '../../../pages/contract/constants'
const getPageFieldMaxLinesAndWordAmount = pageField => {
  const width = pageField.width
  const fsize = pageField.fontSize
  var line = parseInt(pageField.height / (fsize * TextareaLineHeight), 10)
  if (line <= 0) {
    line = 1
  }
  const wordAmount = parseInt(width / fsize, 10)
  return [line, wordAmount * line]
}

export default getPageFieldMaxLinesAndWordAmount
