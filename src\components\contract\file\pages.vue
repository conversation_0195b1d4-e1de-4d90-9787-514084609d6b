<template>
  <div class="pages" v-if="images.length">
    <div
      class="webkit-scrollbar"
      :style="{
        overflow: 'auto',
        height: `calc(100vh - ${top})`,
        padding: '0 16px' //不要改变它，后续有依赖它的计算
        //id不能换，总宽度计算依赖它
      }"
      id="pagesBox"
      ref="pagesBox"
    >
      <div
        :style="{
          //这里是必要的，触发pagesBox滚动 以及协调真实的滚动距离
          height: `${totalImagesHeight * scale}px`,
          width: `${imgSize[0] * scale}px`,
          margin: '0 auto',
          //filepage全部是absolute，这里为他们提供基位
          position: 'relative'
        }"
      >
        <div
          :style="{
            //必须固定宽高，因为缩放只是视觉缩放，并没有真实去改变像素值
            //如果不设置，浏览器缩放将没有基础值，导致缩放基于缩放后再次缩放一次
            height: `${totalImagesHeight}px`,
            width: `${imgSize[0]}px`,
            transformOrigin: '0 top',
            transform: `scale(${scale})`
          }"
        >
          <Page
            :key="index"
            v-for="(src, index) in images"
            :pageNo="index + 1"
            :imgSize="imgSize"
            :src="src"
            :scale="scale"
            @itemDrop="e => $emit('itemDrop', e, fileId, index + 1)"
          >
            <slot v-bind="{ pageNo: index + 1, fileId: fileId }"></slot>
          </Page>
        </div>
      </div>
    </div>
    <Pagination
      v-if="showPagination"
      :imgSize="imgSize"
      :total="images.length"
      :currentScale="scale"
      @scaleChange="scaleChange"
      @pageChange="p => scrollToPage(p)"
    />
    <img :style="{ display: 'none' }" :src="images[0]" @load="imgload" />
  </div>
</template>
<script>
import Page from './page.vue'
import Pagination from './pagination.vue'
export default {
  components: {
    Page,
    Pagination
  },
  props: {
    //高度修正
    top: {
      type: String,
      default() {
        return '88px'
      }
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    fileId: Number,
    //图片地址
    images: Array
  },
  data() {
    return {
      imgSize: [0, 0],
      scale: 1
    }
  },

  computed: {
    //本函数并为考虑传入的文件大小不一情况
    totalImagesHeight() {
      return this.images.length * this.imgSize[1] + this.images.length * 16 //16为两张图片之间的间隙 包含一个顶部间隙
    }
  },
  methods: {
    imgload(e) {
      var width = e.target.width
      var height = e.target.height
      this.imgSize = [width, height]
      const pagesBox = document.getElementById('pagesBox')
      //32 是外围的padding
      const maxWidth = pagesBox.clientWidth - 16 * 2
      //大于pagesBox 则等比缩放
      if (width > maxWidth) {
        const p = maxWidth / width

        this.scale = p
        this.$emit('scaleChange', p)
      }
    },
    scaleChange(nscale) {
      this.scale = nscale
      this.$emit('scaleChange', nscale)
    },
    scrollToFirstPageField(pageField) {
      const page = pageField.pageNo
      const el = this.$refs.pagesBox
      const top = el.scrollTop
      var aPageHeight = this.imgSize[1] + 16
      const P = (aPageHeight * (page - 1) + pageField.coordY) * this.scale
      //避免无意义的滚动
      if (Math.abs(P - top) < 20) {
        return
      }
      el.scroll({
        top: P - 80,
        behavior: 'smooth'
      })
    },
    scrollToPage(page) {
      const el = this.$refs.pagesBox
      var aPageHeight = this.imgSize[1] + 16
      el.scroll({
        top: aPageHeight * (page - 1) * this.scale + 5, // +5 为了解决部分电脑页数展示不正确
        behavior: 'smooth'
      })
      console.log('scroll to page', page)
    }
  }
}
</script>
<style scoped></style>