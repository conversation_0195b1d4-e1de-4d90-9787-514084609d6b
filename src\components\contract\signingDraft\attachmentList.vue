<template>
  <div class="attachmentFiles">
    <div
      :key="file.archiveId"
      v-for="file in files"
      :style="{
        padding: '2px 5px',
        background: '#F7FAFD',
        borderRadius: '8px',
        marginBottom: '10px',
        marginTop: '10px',
        fontSize: '12px'
      }"
      class="text-ellipsis-2line"
    >
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <div>
          <div :title="file.name" class="text-ellipsis-2line">
            {{ file.name }}
          </div>
          <div
            class="text-ellipsis"
            style="color: #777c94"
            v-if="creator && creator.signerType"
          >
            上传人：{{
              creator.signerType === SingerTypePerson
                ? `${creator.signer.name}`
                : `${creator.legal.name}（${creator.signer.name}）`
            }}
          </div>
        </div>
        <el-dropdown @command="type => handleCommand(type, file)">
          <span class="el-dropdown-link">
            <i
              style="color: #777c94; transform: rotate(90deg)"
              class="olading-iconfont oi-icon_other_"
            ></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="!readOnly" command="remove"
              >删除</el-dropdown-item
            >
            <el-dropdown-item command="download">下载</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script>
import store from '../../../helpers/store'
import { SingerTypePerson } from '../../../services/contract/constants'
export default {
  props: {
    files: Array,
    creator: {
      type: Object,
      default: () => {}
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.closed = store.get('__attachmentFilesTipClosed')
  },
  methods: {
    close() {
      store.set('__attachmentFilesTipClosed', true)
      this.closed = true
    },
    handleCommand(type, file) {
      this.$emit(type, file)
    }
  },
  data() {
    return {
      closed: false,
      SingerTypePerson
    }
  }
}
</script>