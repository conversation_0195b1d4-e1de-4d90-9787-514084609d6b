<template>
  <div>
    <div
      @click="skipPage"
      class="text-ellipsis"
      style="
        font-weight: 500;
        font-size: 12px;
        cursor: pointer;
        color: #4f71ff;
        height: 14px;
      "
    >
      {{ row.name }}
    </div>
    <div
      class="text-ellipsis"
      style="font-size: 10px; color: #777c94; margin-top: 6px; height: 14px"
    >
      {{ `发起方：${row.creator.legal.name}(${row.creator.signer.name})` }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContactName',
  props: {
    row: {
      type: Object,
      default: () => {}
    },
    user: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    skipPage() {
      this.$router.push({
        path: `/contracts/${this.row.id}`
      })
    }
  }
}
</script>

<style>
</style>