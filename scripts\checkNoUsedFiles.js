const fs = require("fs")
const path = require("path")

const walk = (fp, fn) => {
  const files = fs.readdirSync(fp)
  for (var f of files) {
    if (f[0] === '.') {
      continue
    }
    const nfp = path.join(fp, f)
    const stats = fs.statSync(nfp)
    if (stats.isDirectory()) {
      walk(nfp, fn)
      continue
    }

    fn(nfp)
  }
}

var usedFiles = {}
var allFiles = []
const calcAllFiles = f => {
  const en = path.extname(f)
  if (!(en === '.vue' || en === '.js')) {
    return
  }

  allFiles.push(f)
}
const calcUsedFiles = f => {
  const en = path.extname(f)
  if (!(en === '.vue' || en === '.js')) {
    return
  }

  const c = fs.readFileSync(f).toString()
  const reg = /from\s+["']([^"']+)["']/g;
  const rs = c.matchAll(reg)

  const cPath = path.dirname(f)
  for (var cc of rs) {
    var fn = cc[1]
    // if (f.includes('informationRegistration')) {
    //   console.log('informationRegistration>', cc[1])
    // }

    //不再是相对路径
    if (cc[1].includes('kit/')) {
      fn = cc[1].replace('kit/', 'src/')
    } else {
      fn = path.normalize(cPath + '/' + fn)
    }

    //补全扩展名
    if (!fn.includes('.vue') && !fn.includes('.js')) {
      fn += '.js'
    }

    usedFiles[fn] = true
  }
}

walk('src', calcAllFiles)
walk('src', calcUsedFiles)

var count = 0
for (var c of allFiles) {
  if (c.includes('src/pages')) {
    continue
  }
  if (c.includes('src/services')) {
    continue
  }
  if (c.includes('index.js')) {
    continue
  }
  if (c.includes('iconfont.js')) {
    continue
  }
  if (c.includes('testData/')) {
    continue
  }
  if (c.includes('src/main.js')) {
    continue
  }
  if (c.includes('.test.js')) {
    continue
  }
  if (c.includes('_test.js')) {
    continue
  }
  if (c.includes('ui/')) {
    continue
  }
  if (c.includes('helpers/')) {
    continue
  }
  if (!usedFiles[c]) {
    count++
    console.log(c, 'no use, please remove')
  }
}

// console.log(allFiles.filter(item=>item.includes('template')))
// console.log(Object.keys(usedFiles).filter(item=>item.includes('template/file')))
if (!count) {
  console.log('✅All right, cool!')
} else {
  console.log('❌Please remove no used files!')
}
