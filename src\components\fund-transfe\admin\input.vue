<template>
  <el-input
    :value="value"
    v-bind="$attrs"
    @input="onInput"
    :maxlength="maxLength"
    :clearable="clearable"
    @blur="onBlur"
    @change="v => $emit('change', v)"
  >
    <template slot="append">
      <slot name="append" />
    </template>
  </el-input>
</template>

<script>
import { roundToTwoDecimals } from 'kit/helpers/roundToTwoDecimals'

export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    valueType: {
      type: String,
      default: ''
    },
    clearable: {
      type: Boolean,
      default: true
    },
    allowZero: {
      type: Boolean,
      default: false
    },
    trimAll: {
      type: Boolean,
      default: false
    },
    trim: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: [String, Number],
      default: 13
    },
    // 最大限额
    limitedAmount: {
      type: [String, Number],
      default: 999999999999.0
    },
    // 是否自动保留两位小数
    autoRetainTwoDecimalPlaces: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    maxLength() {
      const valueType = this.valueType
      const decimalsLen =
        valueType === 'decimals_1' ? 2 : valueType === 'decimals_2' ? 3 : 0
      const maxLength = Number(this.maxlength) + decimalsLen
      return maxLength
    }
  },
  methods: {
    onBlur() {
      if (typeof this.value !== 'string') return

      let value = this.value

      if (this.trim) {
        value = this.value.trim()
      }

      if (this.trimAll) {
        value = this.value.replace(/\s+/g, '')
      }

      if (this.autoRetainTwoDecimalPlaces && this.valueType === 'decimals_2') {
        value = roundToTwoDecimals(value)
      }

      this.$emit('blur', value)
      this.onInput(value)
    },
    onInput(value) {
      const { valueType } = this

      if (!this.valueType) return this.$emit('input', value)

      if (String(value) === '0' && !this.allowZero) {
        return this.$emit('input', '')
      }

      if (this.allowZero && String(value) === '00') {
        return this.$emit('input', 0)
      }

      value = String(value)

      if (!value.includes('.') && value.length > this.maxlength) return

      if (Number(value) > this.limitedAmount) return

      switch (valueType) {
        case 'int':
          value = String(value).replace(/[^0-9]/gi, '')
          break
        case 'decimals_1':
          try {
            if (value.includes('.') && value.length > 12) return
            value = String(value).match(/\d+(\.\d{0,1})?/)[0]
          } catch (err) {
            value = ''
          }
          break
        case 'decimals_2':
          try {
            if (value.includes('.') && value.length > this.maxLength) {
              console.log('return')
              return
            }
            value = String(value).match(/\d+(\.\d{0,2})?/)[0]
            if (value.indexOf('0') === 0 && value.indexOf('.') !== 1)
              value = Number(value)
          } catch (err) {
            value = ''
          }
          break
      }

      this.$emit('input', value)
    }
  }
}
</script>
