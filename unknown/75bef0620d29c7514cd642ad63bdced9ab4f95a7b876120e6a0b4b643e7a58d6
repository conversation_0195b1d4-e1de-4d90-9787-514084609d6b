<template>
  <nav>
    <el-image
      :src="headerLogoUrl"
      style="height: 48px"
      class="logo"
      @click="handleHomeClick"
    ></el-image>
    <ul class="nav-menu">
      <li
        v-for="item in navList"
        :key="item.id"
        :class="{ active: value === item.code }"
      >
        <div @click="handleNavLiClick(item)">
          {{ item.name }}
        </div>
      </li>
    </ul>
    <div class="right flex-center">
      <el-popover
        placement="top-start"
        width="200"
        :disabled="!isSwitchCompanyEnabled"
        trigger="click"
        popper-class="marketing-admin-switch-company-modal"
      >
        <ul class="company-list webkit-scrollbar">
          <li
            :key="item.id"
            v-for="item in profile.merchantList"
            :class="{ active: profile.currentMerchantId === item.id }"
            @click="handleChangeMerchantClick(item)"
          >
            <AutoEllipsisTooltip :content="item.name" />
          </li>
        </ul>
        <div class="company-info flex-center" slot="reference">
          <AutoEllipsisTooltip :content="companyName" class="company-name" />
          <i class="el-icon-caret-bottom" v-if="isSwitchCompanyEnabled" />
        </div>
      </el-popover>

      <el-popover
        placement="top-start"
        width="200"
        trigger="click"
        ref="userInfoPopover"
        popper-class="marketing-admin-switch-company-modal"
      >
        <div class="user-name">{{ profile.userName }}</div>
        <ul class="company-list webkit-scrollbar">
          <li @click="handleLogoutClick">退出登录</li>
        </ul>
        <div class="flex-center avatar-box" slot="reference">
          <div class="avatar">
            <el-image :src="user_default"></el-image>
          </div>
          <i class="el-icon-caret-bottom" />
        </div>
      </el-popover>
    </div>
  </nav>
</template>

<script>
import { delay } from 'kit/helpers/delay'
import logoURL from 'kit/assets/images/fund-transfe/logo.png'
import user_default from 'kit/assets/images/user_default.png'
import AutoEllipsisTooltip from './autoEllipsisTooltip.vue'

export default {
  components: {
    AutoEllipsisTooltip
  },
  data() {
    return {
      isLogoutLoading: false,
      user_default
    }
  },
  props: {
    headerLogoUrl: {
      type: String,
      default: logoURL
    },
    navList: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, null],
      default: ''
    },
    profile: {
      type: Object,
      default: () => {
        return {
          merchantList: [],
          currentMerchantId: '',
          menuTree: [],
          userName: ''
        }
      }
    }
  },
  computed: {
    companyName() {
      const { merchantList, currentMerchantId } = this.profile
      if (currentMerchantId === undefined) return ''
      if ((merchantList && !merchantList.length) || !currentMerchantId) {
        return '暂未加入任何企业'
      }
      const item = merchantList.find(item => item.id === currentMerchantId)
      return item?.name
    },
    isSwitchCompanyEnabled() {
      return this.profile.merchantList?.length > 1
    }
  },
  methods: {
    handleNavLiClick(item) {
      this.$emit('input', item.code)
      this.$emit('change', item)
    },
    handleChangeMerchantClick(item) {
      this.$emit('changeMerchant', item)
    },
    async handleLogoutClick() {
      if (this.isLogoutLoading) return
      this.isLogoutLoading = true
      this.$refs.userInfoPopover.showPopper = false
      setTimeout(() => {
        this.$oConfirm({
          title: '提示',
          message: '是否退出登录？',
          confirm: async () => {
            this.$emit('logout')
          }
        })
      }, 200)
      await delay(1000)
      this.isLogoutLoading = false
    },
    handleHomeClick() {
      this.$emit('home')
    }
  }
}
</script>

<style scoped>
nav {
  position: relative;
  z-index: 1;
  height: 62px;
  opacity: 1;
  background: #ffffffff;
  box-shadow: 0 2px 4px 0 #0000000f, 0 1px 2px 0 #00000005,
    0 1px 2px -2px #0000001a;
  padding: 0 24px;
  display: flex;
  align-items: center;
}
nav .logo {
  cursor: pointer;
}
nav .logo ::v-deep img {
  width: auto;
}
.nav-menu {
  display: flex;
  margin-left: 40px;
  height: 100%;
}
.nav-menu li {
  color: #1e2228ff;
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC';
  text-align: left;
  line-height: 24px;
  margin-right: 32px;
  height: 100%;
  line-height: 62px;
  cursor: pointer;
}
.nav-menu li.active,
.nav-menu li:hover {
  color: var(--o-primary-color);
  position: relative;
}
.nav-menu li:hover::after,
.nav-menu li.active::after {
  height: 2px;
  background-color: var(--o-primary-color);
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
.right {
  flex: 1;
  justify-content: flex-end;
}
.company-info,
.avatar-box {
  cursor: pointer;
}
.company-info .company-name {
  color: #1e2228ff;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  max-width: 250px;
}
.avatar-box {
  margin-left: 20px;
  padding-left: 20px;
  position: relative;
}
.avatar-box::before {
  content: '';
  width: 1px;
  background: #e4e7edff;
  height: 16px;
  left: 0;
  position: absolute;
}
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}
.el-icon-caret-bottom {
  color: #828b9b;
  font-size: 8px;
  margin-left: 4px;
}
.flex-center {
  display: flex;
  align-items: center;
}
.company-list {
  max-height: 300px;
  overflow-y: scroll;
  padding: 12px;
}
.company-list li {
  font-weight: 400;
  font-size: 14px;
  color: #555;
  padding-left: 8px;
  box-sizing: border-box;
  cursor: pointer;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  line-height: 40px;
  margin-bottom: 4px;
}

.company-list li:last-child {
  margin-bottom: 0;
}

.company-list li:hover,
.company-list li.active {
  background: #fff3e9;
  border-radius: 8px;
}
.user-name {
  padding: 20px;
  border-bottom: 1px solid #e9e9e9;
}
</style>
