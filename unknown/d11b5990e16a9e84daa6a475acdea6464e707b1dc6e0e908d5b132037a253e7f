<template>
  <div class="tabs">
    <div
      class="tab-second"
      v-for="item in options"
      :key="item.value"
      :class="{ active: item.value === value }"
      @click="handleClick(item)"
    >
      {{ item.label }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => [],
      require: true
    },
    value: {
      type: [String, Number, null],
      default: null
    }
  },
  methods: {
    handleClick({ value }) {
      this.$emit('input', value)
    }
  }
}
</script>
<style scoped>
.tabs {
  height: 32px;
  border-radius: 6px;
  opacity: 1;
  background: #f2f4f7;
  display: inline-flex;
  padding: 0 2px;
  align-items: center;
}
.tab-second {
  height: 28px;
  border-radius: 4px;
  line-height: 28px;
  color: #1e2228;
  font-size: 14px;
  font-weight: 400;
  padding: 0 12px;
  cursor: pointer;
}
.tab-second.active {
  background: #ffffff;
  box-shadow: 0 1px 2px 0 #00000008, 0 1px 6px -1px #00000005,
    0 2px 4px 0 #00000005;
}
</style>