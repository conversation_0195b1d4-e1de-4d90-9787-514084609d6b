<template>
  <el-radio-group
    v-model="selectedValue"
    :disabled="disabled"
    @change="onInput"
    v-on="$listeners"
  >
    <el-radio
      v-for="option in options"
      :label="option.value"
      :key="option.value"
    >
      {{ option.label }}
    </el-radio>
  </el-radio-group>
</template>

<script>
import deepClone from 'kit/helpers/deepClone'
export default {
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: [String, Boolean, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedValue: deepClone(this.value)
    }
  },
  watch: {
    value() {
      this.selectedValue = deepClone(this.value)
    }
  },
  methods: {
    onInput() {
      this.$emit('input', this.selectedValue)
    }
  }
}
</script>
