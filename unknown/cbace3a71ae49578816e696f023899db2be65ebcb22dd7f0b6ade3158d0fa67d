<template>
  <el-input
    :value="value"
    v-bind="$attrs"
    class="textarea"
    type="textarea"
    clearable
    @input="onInput"
    v-on="$listeners"
    :show-word-limit="true"
    @blur="onBlur"
  />
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    trim: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value.trim())
    },
    onBlur() {
      let value = this.trim ? this.value.trim() : this.value
      this.$emit('blur', value)
      this.onInput(value)
    }
  }
}
</script>
<style scoped>
.textarea ::v-deep .el-input__count {
  bottom: -32px;
  right: 5px;
}
</style>
