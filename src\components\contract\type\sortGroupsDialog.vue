<template>
  <el-dialog
    v-bind="$attrs"
    v-on="$listeners"
    title="分组排序"
    top="60px"
    @open="openDialog"
    @close="closeDialog"
    :close-on-click-modal="false"
  >
    <div style="color: #ccc">请拖拽分组进行排序</div>
    <div
      class="webkit-scrollbar"
      :style="{
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 260px)'
      }"
    >
      <DraggableList v-model="needSortItems">
        <template v-slot="{ item }">
          <div
            :style="{
              padding: '15px',
              width: '300px',
              border: '1px solid #ccc',
              borderRadius: '8px'
            }"
          >
            {{ item.name }} ({{ item.length }})
          </div>
        </template>
      </DraggableList>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible')">取消</el-button>
      <el-button type="primary" @click="$emit('submit', needSortItems)">
        提交
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import DraggableList from '../draggableList.vue'
export default {
  methods: {
    openDialog() {
      this.initNeedSortItems()
    },
    closeDialog() {
      this.initNeedSortItems()
    },
    initNeedSortItems() {
      var r = []
      for (var c of this.groups) {
        r.push({
          id: c.groupId,
          name: c.groupName,
          sort: c.sort,
          length: c.type ? c.type.length : 0
        })
      }
      console.log('r=', r)
      this.needSortItems = r
    }
  },
  props: {
    groups: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      needSortItems: []
    }
  },
  components: {
    DraggableList
  }
}
</script>