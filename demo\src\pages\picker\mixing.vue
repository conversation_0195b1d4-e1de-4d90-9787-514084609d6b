<template>
  <Mixing
    :title="'混合多选'"
    :departments="departments"
    :employees="employees"
    :selectedDepartments="selectedDepartments"
    :selectedEmployees="selectedEmployees"
    @selectEmployee="selectEmployee"
    @unselectEmployee="unselectEmployee"
    @selectAllEmployees="selectAllEmployees"
    @unselectAllEmployees="unselectAllEmployees"
    @selectDepartment="selectDepartment"
    @unselectDepartment="unselectDepartment"
    @selectAllDepartments="selectAllDepartments"
    @unselectAllDepartments="unselectAllDepartments"
    @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
    @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
    @clear="clear"
  />
</template>

<script>
import Mixing from 'kit/components/ui/picker/mixing.vue'
import testDepartments from './testDepartments'
import testEmployees from './testEmployees'
export default {
  components: {
    Mixing
  },
  data() {
    return {
      departments: testDepartments,
      employees: testEmployees,
      breadcrumbDepartments: [
        {
          id: 0,
          name: '根部门'
        }
      ],
      selectedDepartments: [],
      selectedEmployees: []
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedEmployee)
    },
    cancel() {
      this.selectedEmployee = null
      console.log('cancel', this.selectedEmployee)
    },
    handleClickDepartmentSubdivision(v) {
      this.departments = v.children
    },
    handleClickBreadcrumbDepartment(v) {
      this.departments = testDepartments
    },
    search(v) {
      console.log('search', v)
    },
    selectEmployee(v) {
      const n = [...this.selectedEmployees]
      n.push(v)
      this.selectedEmployees = n
    },
    unselectEmployee(v) {
      console.log('unselect', v)
      this.selectedEmployees = this.selectedEmployees.filter(
        item => item.id !== v.id
      )
    },
    selectAllEmployees() {
      this.selectedEmployees = this.employees.filter(item => !item.disabled)
    },
    unselectAllEmployees() {
      this.selectedEmployees = []
    },
    selectDepartment(v) {
      const n = [...this.selectedDepartments]
      n.push(v)
      this.selectedDepartments = n
    },
    unselectDepartment(v) {
      console.log('unselect', v)
      this.selectedDepartments = this.selectedDepartments.filter(
        item => item.id !== v.id
      )
    },
    selectAllDepartments() {
      this.selectedDepartments = this.departments.filter(item => !item.disabled)
    },
    unselectAllDepartments() {
      this.selectedDepartments = []
    },
    clear() {
      this.selectedEmployees = []
      this.selectedDepartments = []
    }
  }
}
</script>

<style scoped>
</style>