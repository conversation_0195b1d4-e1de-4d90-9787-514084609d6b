<body>
  <div id="origin" style="display: flex"></div>
  <div id="origin20" style="display: flex"></div>
</body>
<script>
  const colors = [
    '#4F71FF',
    '#FFAC04',
    '#07BB06',
    '#71248D',
    '#c65306',
    '#006e5f',
    '#faff72'
  ]
  const createDiv = (c, percent) => {
    var div = document.createElement('div')
    div.style.height = '100px'
    div.style.width = '100px'
    div.style.margin = '10px'
    if (percent) {
      div.style.background = `${c}${percent}`
    } else {
      div.style.background = `${c}`
    }

    div.style.border = `1px dashed ${c}`

    return div
  }
  const origin = document.getElementById('origin')
  const origin20 = document.getElementById('origin20')
  for (var c of colors) {
    origin.appendChild(createDiv(c, 90))
    origin20.appendChild(createDiv(c, 20))
  }
</script>