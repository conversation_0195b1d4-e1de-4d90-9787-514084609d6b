<template>
  <el-dialog
    :close-on-click-modal="false"
    v-bind="$attrs"
    v-on="$listeners"
    width="500px"
    title="分组排序"
    top="60px"
    @open="openDialog"
    @close="closeDialog"
  >
    <div style="color: #ccc">请拖拽分组进行排序</div>
    <div
      class="webkit-scrollbar"
      :style="{
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 260px)',
        padding: '10px'
      }"
    >
      <DraggableList v-model="needSortItems">
        <template v-slot="{ item }">
          <div
            :style="{
              padding: '15px',
              width: '300px',
              border: '1px solid #ccc',
              borderRadius: '8px'
            }"
          >
            {{ item.name }} ({{ item.length }})
          </div>
        </template>
      </DraggableList>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible')">取消</el-button>
      <el-button type="primary" @click="$emit('submit', needSortItems)">
        提交
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import DraggableList from '../draggableList.vue'
export default {
  methods: {
    openDialog() {
      this.initNeedSortItems()
    },
    closeDialog() {
      this.initNeedSortItems()
    },
    initNeedSortItems() {
      var r = []
      for (var c of this.groups) {
        r.push({
          id: c.id,
          name: c.name,
          sort: c.sort,
          length: c.approves ? c.approves.length : 0
        })
      }
      console.log('r=', r)
      this.needSortItems = r
    }
  },
  created() {
    console.log('this.groups', this.groups)
  },
  props: {
    open: {
      type: Boolean,
      default() {
        return false
      }
    },
    groups: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      needSortItems: []
    }
  },
  components: {
    DraggableList
  }
}
</script>